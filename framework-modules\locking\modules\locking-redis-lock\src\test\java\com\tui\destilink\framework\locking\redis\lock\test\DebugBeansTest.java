package com.tui.destilink.framework.locking.redis.lock.test;

import com.tui.destilink.framework.test.support.redis.annotation.RedisTestSupport;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.test.annotation.DirtiesContext;

import static org.assertj.core.api.Assertions.assertThat;

@RedisTestSupport(keyspacePrefixes = { "test-locks:" })
@SpringBootTest(classes = TestApplication.class, webEnvironment = SpringBootTest.WebEnvironment.NONE)
@DirtiesContext(classMode = DirtiesContext.ClassMode.BEFORE_EACH_TEST_METHOD)
public class DebugBeansTest {

    @Autowired
    private ApplicationContext applicationContext;

    @Test
    void debugAvailableBeans() {
        System.out.println("=== Available Beans ===");
        
        // Check connection factory
        String[] connectionFactoryBeans = applicationContext.getBeanNamesForType(RedisConnectionFactory.class);
        System.out.println("RedisConnectionFactory beans: " + java.util.Arrays.toString(connectionFactoryBeans));
        
        if (connectionFactoryBeans.length > 0) {
            RedisConnectionFactory connectionFactory = applicationContext.getBean(connectionFactoryBeans[0], RedisConnectionFactory.class);
            System.out.println("Connection factory type: " + connectionFactory.getClass().getName());
            
            if (connectionFactory instanceof LettuceConnectionFactory) {
                LettuceConnectionFactory lettuceFactory = (LettuceConnectionFactory) connectionFactory;
                Object nativeClient = lettuceFactory.getNativeClient();
                System.out.println("Native client type: " + (nativeClient != null ? nativeClient.getClass().getName() : "null"));
            }
        }
        
        // Check cluster client
        String[] clusterClientBeans = applicationContext.getBeanNamesForType(io.lettuce.core.cluster.RedisClusterClient.class);
        System.out.println("RedisClusterClient beans: " + java.util.Arrays.toString(clusterClientBeans));
        
        // Check cluster command executor
        String[] commandExecutorBeans = applicationContext.getBeanNamesForType(com.tui.destilink.framework.redis.core.cluster.ClusterCommandExecutor.class);
        System.out.println("ClusterCommandExecutor beans: " + java.util.Arrays.toString(commandExecutorBeans));
        
        assertThat(connectionFactoryBeans).isNotEmpty();
    }
}
