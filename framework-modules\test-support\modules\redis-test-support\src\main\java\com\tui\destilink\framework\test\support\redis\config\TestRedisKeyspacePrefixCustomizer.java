package com.tui.destilink.framework.test.support.redis.config;

import com.tui.destilink.framework.redis.core.config.RedisCoreProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.util.StringUtils;

/**
 * Bean post processor that customizes Redis keyspace prefixes for test environments.
 * <p>
 * This processor automatically prepends the test-specific unique ID to both application
 * and distributed keyspace prefixes in {@link RedisCoreProperties.KeyspacePrefixes}.
 * This ensures test isolation by preventing key collisions between different test executions.
 * <p>
 * The unique ID prefix follows the pattern: {@code uniqueId:originalPrefix}
 * <p>
 * Features:
 * <ul>
 *   <li>Preserves user-defined custom keyspace prefixes from application.yaml</li>
 *   <li>Maintains the original prefix format while adding test isolation</li>
 *   <li>Handles both placeholder-based and literal prefix values</li>
 *   <li>Only processes {@link RedisCoreProperties} beans</li>
 * </ul>
 *
 * @see RedisCoreProperties.KeyspacePrefixes
 * @see TestConfigProvider
 */
@Slf4j
public class TestRedisKeyspacePrefixCustomizer implements BeanPostProcessor {

    private final String uniqueId;

    /**
     * Creates a new customizer instance.
     *
     * @param uniqueId The unique test identifier to prepend to keyspace prefixes
     */
    public TestRedisKeyspacePrefixCustomizer(String uniqueId) {
        this.uniqueId = uniqueId;
    }

    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) {
        if (bean instanceof RedisCoreProperties redisCoreProperties) {
            customizeKeyspacePrefixes(redisCoreProperties);
        }
        return bean;
    }

    /**
     * Customizes the keyspace prefixes in the provided RedisCoreProperties instance.
     * <p>
     * This method modifies both application and distributed prefixes by prepending
     * the unique test ID. The modification preserves any custom prefixes that may
     * have been configured in application.yaml.
     *
     * @param properties The RedisCoreProperties instance to customize
     */
    private void customizeKeyspacePrefixes(RedisCoreProperties properties) {
        RedisCoreProperties.KeyspacePrefixes keyspacePrefixes = properties.getKeyspacePrefixes();
        
        // Customize application prefix
        String originalApplicationPrefix = keyspacePrefixes.getApplication();
        String customizedApplicationPrefix = prependUniqueId(originalApplicationPrefix);
        keyspacePrefixes.setApplication(customizedApplicationPrefix);
        
        // Customize distributed prefix
        String originalDistributedPrefix = keyspacePrefixes.getDistributed();
        String customizedDistributedPrefix = prependUniqueId(originalDistributedPrefix);
        keyspacePrefixes.setDistributed(customizedDistributedPrefix);
        
        log.debug("Customized Redis keyspace prefixes for test isolation: " +
                "application=[{}] -> [{}], distributed=[{}] -> [{}]",
                originalApplicationPrefix, customizedApplicationPrefix,
                originalDistributedPrefix, customizedDistributedPrefix);
    }

    /**
     * Prepends the unique ID to a keyspace prefix string.
     * <p>
     * The resulting format is: {@code uniqueId:originalPrefix}
     * <p>
     * Special handling:
     * <ul>
     *   <li>Preserves placeholder expressions like ${spring.application.name}</li>
     *   <li>Handles empty or null prefixes gracefully</li>
     *   <li>Ensures proper delimiter placement</li>
     * </ul>
     *
     * @param originalPrefix The original keyspace prefix (may contain placeholders)
     * @return The prefix with unique ID prepended
     */
    private String prependUniqueId(String originalPrefix) {
        if (!StringUtils.hasText(originalPrefix)) {
            return uniqueId;
        }
        
        // Handle placeholder expressions specially to maintain their functionality
        if (originalPrefix.contains("${") && originalPrefix.contains("}")) {
            // For placeholder expressions, we need to construct a new expression
            // that resolves to uniqueId:resolvedValue
            return uniqueId + ":" + originalPrefix;
        }
        
        // For literal prefixes, directly prepend the unique ID
        return uniqueId + ":" + originalPrefix;
    }
}
