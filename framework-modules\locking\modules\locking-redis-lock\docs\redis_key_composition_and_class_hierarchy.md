# Redis Key Composition and Class Hierarchy Documentation

## Table of Contents
1. [Introduction](#1-introduction)
2. [Redis Key Schema Architecture](#2-redis-key-schema-architecture)
3. [Class Hierarchy and Key Assembly Process](#3-class-hierarchy-and-key-assembly-process)
4. [Redis Key Composition Examples](#4-redis-key-composition-examples)
5. [Pub/Sub Channel Schema](#5-pubsub-channel-schema)
6. [Hash Tags and Redis Cluster Compatibility](#6-hash-tags-and-redis-cluster-compatibility)
7. [Idempotency and Response Caching](#7-idempotency-and-response-caching)
8. [Atomic Operations with Lua Scripts](#8-atomic-operations-with-lua-scripts)

## 1. Introduction

This document provides a comprehensive overview of the Redis key composition, class hierarchy, and key assembly process within the `locking-redis-lock` module of the Destilink Framework. The module implements a sophisticated distributed locking mechanism with:

- **Asynchronous-First Operations** with Virtual Threads
- **Atomicity via <PERSON><PERSON>ripts** for all Redis operations
- **Centralized Idempotency Management** with response caching
- **Lock-Type Semantic Isolation** through mandatory lock-type segments
- **Redis Cluster Compatibility** with hash tags

## 2. Redis Key Schema Architecture

### 2.1 Key Structure Principles

The Redis key schema follows a hierarchical structure with mandatory components:

```
<prefix>:<bucketName>:<namespace>:<lockType>:{<lockName>}[:<suffix>]
```

### 2.2 Key Components Breakdown

```mermaid
graph TD
    A[Redis Key Components] --> B[Prefix]
    A --> C[Bucket Name]
    A --> D[Namespace]
    A --> E[Lock Type]
    A --> F[Lock Name with Hash Tag]
    A --> G[Optional Suffix]
    
    B --> B1[application/distributed]
    B --> B2[From RedisCoreProperties]
    
    C --> C1[Configured via Builders]
    C --> C2[Groups related locks]
    
    D --> D1[__locks__]
    D --> D2[__unlock_channels__]
    D --> D3[__resp_cache__]
    D --> D4[__rwttl__]
    
    E --> E1[reentrant]
    E --> E2[stamped]
    E --> E3[state]
    E --> E4[readwrite]
    
    F --> F1["Hash Tag: &lcub;lockName&rcub;"]
    F --> F2[Redis Cluster Co-location]
    
    G --> G1[:data]
    G --> G2[:requestUuid]
    G --> G3[:specific-operation]
```

### 2.3 Key Types and Purposes

| Key Type | Format | Purpose |
|----------|--------|---------|
| **Primary Lock Key** | `<prefix>:<bucket>:__locks__:<type>:{<name>}` | Main lock storage with TTL |
| **Lock Data Key** | `<prefix>:<bucket>:__locks__:<type>:{<name>}:data` | Metadata storage (Hash) |
| **Response Cache Key** | `<prefix>:<bucket>:__resp_cache__:<type>:{<name>}:<uuid>` | Idempotency cache |
| **Unlock Channel** | `<prefix>:<bucket>:__unlock_channels__:<type>:{<name>}` | Pub/Sub notifications |
| **Read Lock TTL Key** | `<prefix>:<bucket>:__rwttl__:<type>:{<name>}:<readerId>` | Individual read lock TTLs |

## 3. Class Hierarchy and Key Assembly Process

### 3.1 Complete Class Hierarchy

```mermaid
classDiagram
    class RedisCoreProperties {
        +KeyspacePrefixes keyspacePrefixes
        +String getApplicationPrefix()
        +String getDistributedPrefix()
    }
    
    class RedisLockProperties {
        +Boolean enabled
        +Duration stateKeyExpiration
        +Duration responseCacheTtl
        +Defaults defaults
        +WatchdogProperties watchdog
    }
    
    class LockBucketRegistry {
        +createBucket(String bucketName)
        +LockBucketConfig getBucketConfig(String name)
    }
    
    class LockBucketConfig {
        +String bucketName
        +RedisKeyPrefix keyPrefix
        +RedisLockProperties.Defaults defaults
    }
    
    class AbstractLockTypeConfigBuilder~T~ {
        +T withBucketName(String name)
        +T withLeaseTime(Duration leaseTime)
        +T withRetryInterval(Duration interval)
        +T withMaxRetries(int retries)
    }
    
    class ReentrantLockConfigBuilder {
        +RedisReentrantLock build()
    }
    
    class ReadWriteLockConfigBuilder {
        +RedisReadWriteLock build()
    }
    
    class StateLockConfigBuilder {
        +RedisStateLock build()
    }
    
    class StampedLockConfigBuilder {
        +RedisStampedLock build()
    }
    
    class RedisLockOperations {
        <<interface>>
        +tryLock(...)
        +unlock(...)
        +tryReadLock(...)
        +tryWriteLock(...)
        +tryStateLock(...)
    }
    
    class RedisLockOperationsImpl {
        +generateRequestUuid()
        +getResponseCacheTtlSeconds()
        +executeWithRetry(...)
        -ClusterCommandExecutor executor
        -ScriptLoader scriptLoader
    }
    
    class AbstractRedisLock {
        +String lockKey
        +String getResponseCacheKey()
        +Duration getResponseCacheTtlSeconds()
        #doTryLock(...)
        #doUnlock(...)
    }
    
    class RedisReentrantLock {
        +doTryLock(...)
        +doUnlock(...)
    }
    
    class RedisReadWriteLock {
        -RedisReadLock readLock
        -RedisWriteLock writeLock
    }
    
    class RedisReadLock {
        +doTryLock(...)
        +doUnlock(...)
    }
    
    class RedisWriteLock {
        +doTryLock(...)
        +doUnlock(...)
    }
    
    class RedisStateLock {
        +doTryLock(...)
        +doUnlock(...)
        +updateStateAsync(...)
    }
    
    class RedisStampedLock {
        +tryWriteLockAsync()
        +tryReadLockAsync()
        +tryOptimisticReadAsync()
    }
    
    RedisCoreProperties --> LockBucketRegistry : provides keyspace prefix
    RedisLockProperties --> LockBucketRegistry : provides defaults
    LockBucketRegistry --> LockBucketConfig : creates
    LockBucketConfig --> AbstractLockTypeConfigBuilder : configures
    AbstractLockTypeConfigBuilder <|-- ReentrantLockConfigBuilder
    AbstractLockTypeConfigBuilder <|-- ReadWriteLockConfigBuilder
    AbstractLockTypeConfigBuilder <|-- StateLockConfigBuilder
    AbstractLockTypeConfigBuilder <|-- StampedLockConfigBuilder
    RedisLockOperations <|.. RedisLockOperationsImpl
    AbstractRedisLock --> RedisLockOperations : uses
    AbstractRedisLock <|-- RedisReentrantLock
    AbstractRedisLock <|-- RedisStateLock
    AbstractRedisLock <|-- RedisStampedLock
    RedisReadWriteLock *-- RedisReadLock : contains
    RedisReadWriteLock *-- RedisWriteLock : contains
    AbstractRedisLock <|-- RedisReadLock
    AbstractRedisLock <|-- RedisWriteLock
```

### 3.2 Key Assembly Process Flow

```mermaid
sequenceDiagram
    participant Client
    participant Builder as LockConfigBuilder
    participant Registry as LockBucketRegistry
    participant Config as LockBucketConfig
    participant Lock as AbstractRedisLock
    participant Ops as RedisLockOperationsImpl
    participant Redis as Redis Cluster
    
    Note over Client,Redis: Key Assembly Process
    
    Client->>Builder: Create lock builder
    Note over Builder: Has: lockName only
    
    Builder->>Registry: withBucketName("orders")
    Note over Registry: Has RedisCoreProperties.keyspacePrefix, Creates prefix + __lock_buckets__ + bucketName
    
    Registry->>Config: Create LockBucketConfig
    Note over Config: Has bucketName=orders, keyPrefix=app__lock_buckets__orders, defaults from RedisLockProperties
    
    Config->>Builder: Configure builder
    Builder->>Lock: build() creates lock instance
    
    Note over Lock: Constructs lockKey: app__lock_buckets__orders__locks__reentrant__order123
    
    Lock->>Ops: Call operation (e.g., tryLock)
    Note over Ops: Generates requestUuid, Creates responseCacheKey, Executes Lua script
    
    Ops->>Redis: Execute atomic Lua script
    Note over Redis: All related keys use same hash tag order123
```

### 3.3 Key Assembly at Each Layer

#### Layer 1: RedisCoreProperties
```java
// Only has keyspace prefix
String keyspacePrefix = "application"; // or "distributed"
```

#### Layer 2: LockBucketRegistry
```java
// Appends bucket infrastructure
String bucketPrefix = keyspacePrefix + ":__lock_buckets__:" + bucketName;
// Example: "application:__lock_buckets__:orders"
```

#### Layer 3: LockBucketConfig
```java
// Has complete bucket configuration
RedisKeyPrefix keyPrefix = RedisKeyPrefix.of(bucketPrefix);
// Stores defaults from RedisLockProperties
```

#### Layer 4: Lock Builders
```java
// Append lock-specific segments
String lockTypeSegment = ":__locks__:" + lockType;
// Example: ":__locks__:reentrant"
```

#### Layer 5: AbstractRedisLock
```java
// Complete primary lock key
String lockKey = bucketPrefix + ":__locks__:" + lockType + ":{" + lockName + "}";
// Example: "application:__lock_buckets__:orders:__locks__:reentrant:{order123}"

// Derived keys
String lockDataKey = lockKey + ":data";
String responseCacheKey = lockKey.replace(":__locks__:", ":__resp_cache__:") + ":" + requestUuid;
```

## 4. Redis Key Composition Examples

### 4.1 Reentrant Lock Example

```mermaid
graph LR
    A[application] --> B[:__lock_buckets__:]
    B --> C[orders]
    C --> D[:__locks__:]
    D --> E[reentrant]
    E --> F["&lcub;order123&rcub;"]
    
    style A fill:#e1f5fe
    style C fill:#f3e5f5
    style E fill:#e8f5e8
    style F fill:#fff3e0
```

**Complete Keys:**
- **Primary Lock**: `application:__lock_buckets__:orders:__locks__:reentrant:{order123}`
- **Lock Data**: `application:__lock_buckets__:orders:__locks__:reentrant:{order123}:data`
- **Response Cache**: `application:__lock_buckets__:orders:__resp_cache__:reentrant:{order123}:uuid-12345`
- **Unlock Channel**: `application:__lock_buckets__:orders:__unlock_channels__:reentrant:{order123}`

### 4.2 Read-Write Lock Example

For a read-write lock managing `{document456}`:

```mermaid
graph TB
    subgraph "Read-Write Lock Keys"
        A["Primary RW Hash Key:<br/>app:__lock_buckets__:docs:__locks__:readwrite:&lcub;document456&rcub;"]
        B["Lock Data Key:<br/>app:__lock_buckets__:docs:__locks__:readwrite:&lcub;document456&rcub;:data"]
        C["Individual Read Lock TTL:<br/>app:__lock_buckets__:docs:__rwttl__:readwrite:&lcub;document456&rcub;:reader-uuid-1"]
        D["Unlock Channel:<br/>app:__lock_buckets__:docs:__unlock_channels__:readwrite:&lcub;document456&rcub;"]
        E["Response Cache:<br/>app:__lock_buckets__:docs:__resp_cache__:readwrite:&lcub;document456&rcub;:req-uuid-789"]
    end
    
    style A fill:#ffebee
    style B fill:#e8f5e8
    style C fill:#e3f2fd
    style D fill:#fff8e1
    style E fill:#f3e5f5
```

### 4.3 State Lock Example

For a state lock managing workflow `{workflow789}`:

```
Primary Lock: distributed:__lock_buckets__:workflows:__locks__:state:{workflow789}
Lock Data:    distributed:__lock_buckets__:workflows:__locks__:state:{workflow789}:data
State Key:    distributed:__lock_buckets__:workflows:__locks__:state:{workflow789}:state
Response Cache: distributed:__lock_buckets__:workflows:__resp_cache__:state:{workflow789}:uuid-abc123
Unlock Channel: distributed:__lock_buckets__:workflows:__unlock_channels__:state:{workflow789}
```

### 4.4 Stamped Lock Example

For a stamped lock managing `{counter555}`:

```
Primary Lock: application:__lock_buckets__:counters:__locks__:stamped:{counter555}
Stamped Data: application:__lock_buckets__:counters:__locks__:stamped:{counter555}:data
Response Cache: application:__lock_buckets__:counters:__resp_cache__:stamped:{counter555}:uuid-def456
Unlock Channel: application:__lock_buckets__:counters:__unlock_channels__:stamped:{counter555}
```

## 5. Pub/Sub Channel Schema

### 5.1 Channel Naming Convention

```mermaid
graph LR
    A[Prefix] --> B[Bucket]
    B --> C[:__unlock_channels__:]
    C --> D[Lock Type]
    D --> E[Hash Tag]
    
    F[Publisher: Lua Scripts] --> G[Channel Pattern]
    H[Subscriber: UnlockMessageListener] --> G
    
    G --> I["prefix:bucket:__unlock_channels__:locktype:&lcub;lockname&rcub;"]
    
    style G fill:#fff3e0
    style I fill:#e8f5e8
```

### 5.2 Message Flow

```mermaid
sequenceDiagram
    participant Lock as Lock Instance
    participant Lua as Lua Script
    participant Redis as Redis Pub/Sub
    participant Listener as UnlockMessageListener
    participant Waiter as Waiting Lock Instance
    
    Lock->>Lua: Execute unlock script
    Lua->>Redis: PUBLISH channel "UNLOCK_TYPE"
    Note over Redis: Channel: prefix:bucket:__unlock_channels__:type:{name}
    Redis->>Listener: Message received
    Note over Listener: Extracts lockName and lockType from channel
    Listener->>Waiter: Notify via LockSemaphoreHolder
    Waiter->>Waiter: Complete waiting CompletableFuture
```

### 5.3 Channel Examples

| Lock Type | Channel Pattern | Example |
|-----------|----------------|---------|
| **Reentrant** | `{prefix}:{bucket}:__unlock_channels__:reentrant:{name}` | `app:__lock_buckets__:orders:__unlock_channels__:reentrant:{order123}` |
| **Read-Write** | `{prefix}:{bucket}:__unlock_channels__:readwrite:{name}` | `app:__lock_buckets__:docs:__unlock_channels__:readwrite:{doc456}` |
| **State** | `{prefix}:{bucket}:__unlock_channels__:state:{name}` | `dist:__lock_buckets__:workflows:__unlock_channels__:state:{wf789}` |
| **Stamped** | `{prefix}:{bucket}:__unlock_channels__:stamped:{name}` | `app:__lock_buckets__:counters:__unlock_channels__:stamped:{cnt555}` |

## 6. Hash Tags and Redis Cluster Compatibility

### 6.1 Hash Tag Purpose

Redis Cluster uses hash tags to ensure related keys are stored on the same node:

```mermaid
graph TB
    subgraph "Redis Cluster Node 1"
        A["Primary Lock: ...reentrant:&lcub;order123&rcub;"]
        B["Lock Data: ...reentrant:&lcub;order123&rcub;:data"]
        C["Response Cache: ...reentrant:&lcub;order123&rcub;:uuid-abc"]
        D["Unlock Channel: ...reentrant:&lcub;order123&rcub;"]
    end
    
    subgraph "Redis Cluster Node 2"
        E["Other Keys: ...reentrant:&lcub;order456&rcub;"]
    end
    
    classDef nodeGroup fill:#e1f5fe
    class A,B,C,D nodeGroup
```

**Note:** All keys with `{order123}` hash tag are co-located on the same Redis Cluster node.

### 6.2 Hash Tag Format

```java
// Hash tag extraction
String lockName = "order123";
String hashTag = "{" + lockName + "}";  // Results in: {order123}

// All related keys use the same hash tag
String primaryKey = "app:bucket:__locks__:reentrant:{order123}";
String dataKey = "app:bucket:__locks__:reentrant:{order123}:data";
String cacheKey = "app:bucket:__resp_cache__:reentrant:{order123}:uuid";
```

### 6.3 Lua Script Multi-Key Operations

Lua scripts can operate on multiple keys atomically because they're co-located:

```lua
-- All keys guaranteed to be on same Redis node
local lockKey = KEYS[1]      -- app:bucket:__locks__:reentrant:{order123}
local dataKey = KEYS[2]      -- app:bucket:__locks__:reentrant:{order123}:data  
local cacheKey = KEYS[3]     -- app:bucket:__resp_cache__:reentrant:{order123}:uuid
local channelKey = KEYS[4]   -- app:bucket:__unlock_channels__:reentrant:{order123}

-- Atomic operations across all related keys
redis.call('SET', lockKey, ownerId)
redis.call('HSET', dataKey, 'originalLeaseTimeMillis', ttl)
redis.call('PSETEX', cacheKey, cacheTtl, result)
redis.call('PUBLISH', channelKey, 'LOCK_ACQUIRED')
```

## 7. Idempotency and Response Caching

### 7.1 Idempotency Architecture

```mermaid
graph TD
    A[Client Request] --> B[RedisLockOperationsImpl.generateRequestUuid]
    B --> C[Check Response Cache]
    C --> D{Cache Hit?}
    
    D -->|Yes| E[Return Cached Result]
    D -->|No| F[Execute Lua Script]
    
    F --> G[Store Result in Cache]
    G --> H[Return Result to Client]
    
    I[Retry Request<br/>Same requestUuid] --> C
    
    style E fill:#e8f5e8
    style G fill:#fff3e0
```

### 7.2 Response Cache Key Format

```java
// Response cache key composition
String responseCacheKey = lockKey.replace(":__locks__:", ":__resp_cache__:") + ":" + requestUuid;

// Example transformations:
// From: "app:bucket:__locks__:reentrant:{order123}"
// To:   "app:bucket:__resp_cache__:reentrant:{order123}:uuid-12345"
```

### 7.3 Idempotency in Lua Scripts

```lua
-- Mandatory idempotency wrapper pattern
local responseCacheKey = KEYS[1]
local requestUuid = ARGV[1]
local responseCacheTtl = ARGV[2]

-- Check for cached response
local cachedResult = redis.call('GET', responseCacheKey)
if cachedResult then
    return cjson.decode(cachedResult)
end

-- Execute core operation logic
local result = performLockOperation()

-- Cache the result
redis.call('PSETEX', responseCacheKey, responseCacheTtl, cjson.encode(result))

return result
```

### 7.4 Response Cache Management

| Property | Value | Description |
|----------|-------|-------------|
| **TTL Source** | `RedisLockProperties.responseCacheTtl` | Default cache TTL |
| **Key Format** | `prefix:bucket:__resp_cache__:type:{name}:uuid` | Unique per request |
| **Content** | JSON-encoded operation result | Preserves exact response |
| **Cleanup** | Automatic TTL expiration | No manual cleanup needed |

## 8. Atomic Operations with Lua Scripts

### 8.1 Script Loading and Execution

```mermaid
graph LR
    A[ScriptLoader] --> B[Load Lua Scripts]
    B --> C[Convert to ImmutableLettuceScript]
    C --> D[RedisLockOperationsImpl]
    D --> E[ClusterCommandExecutor]
    E --> F[Redis Cluster]
    
    G[Lock Operation Request] --> D
    D --> H[Generate requestUuid]
    H --> I[Construct Keys Array]
    I --> J[Construct Args Array]
    J --> E
    
    style B fill:#e1f5fe
    style H fill:#fff3e0
```

### 8.2 Key Construction in Operations

```java
// Example: tryLock operation in RedisLockOperationsImpl
public CompletableFuture<Boolean> tryLock(String lockKey, String ownerId, Duration ttl) {
    return executeWithRetry("tryLock", () -> {
        // 1. Generate requestUuid centrally for idempotency
        String requestUuid = generateRequestUuid();
        
        // 2. Construct all related keys
        String lockDataKey = lockKey + ":data";
        String responseCacheKey = lockKey.replace(":__locks__:", ":__resp_cache__:") + ":" + requestUuid;
        
        // 3. Prepare keys array for Lua script
        String[] keys = new String[] { 
            responseCacheKey,    // KEYS[1] - For idempotency
            lockKey,            // KEYS[2] - Primary lock
            lockDataKey         // KEYS[3] - Lock metadata
        };
        
        // 4. Prepare arguments array
        String[] args = new String[] {
            requestUuid,                    // ARGV[1] - Request UUID
            getResponseCacheTtlSeconds(),   // ARGV[2] - Cache TTL
            ownerId,                        // ARGV[3] - Lock owner
            String.valueOf(ttl.toMillis()), // ARGV[4] - Lock TTL
            "reentrant"                     // ARGV[5] - Lock type
        };
        
        // 5. Execute atomic Lua script
        return clusterCommandExecutor.executeScript(lockKey, tryLockScript, keys, args);
    });
}
```

### 8.3 Script Types and Key Usage

| Script Type | Primary Keys Used | Purpose |
|-------------|-------------------|---------|
| **tryLock** | lockKey, lockDataKey, responseCacheKey | Acquire lock atomically |
| **unlock** | lockKey, lockDataKey, responseCacheKey, unlockChannel | Release lock and notify |
| **tryReadLock** | lockKey, lockDataKey, responseCacheKey, readTtlKey | Acquire read lock |
| **tryWriteLock** | lockKey, lockDataKey, responseCacheKey | Acquire write lock |
| **updateState** | lockKey, stateKey, responseCacheKey | Update state atomically |
| **watchdogRefresh** | lockKey, responseCacheKey | Extend lock TTL |

### 8.4 Virtual Thread Integration

```mermaid
sequenceDiagram
    participant Client as Client Thread
    participant Lock as AbstractRedisLock
    participant VT as Virtual Thread Executor
    participant Ops as RedisLockOperationsImpl
    participant Redis as Redis Cluster
    
    Client->>Lock: lock()
    Note over Lock: Capture MDC context
    Lock->>VT: Submit to Virtual Thread
    
    Note over VT: Virtual Thread execution
    VT->>Ops: Call Redis operation
    Ops->>Ops: Generate requestUuid
    Ops->>Ops: Construct key arrays
    Ops->>Redis: Execute Lua script
    Redis-->>Ops: Script result
    Ops-->>VT: Operation result
    VT-->>Lock: Complete future
    Lock-->>Client: Block until complete
    
    Note over Client,Redis: MDC context preserved throughout
```

## Conclusion

This documentation provides a comprehensive overview of the Redis key composition and class hierarchy within the `locking-redis-lock` module. The architecture ensures:

1. **Consistent Key Schema** across all lock types with mandatory lock-type segments
2. **Redis Cluster Compatibility** through proper hash tag usage
3. **Centralized Idempotency** with response caching for operation safety
4. **Atomic Operations** via Lua scripts for consistency and performance
5. **Scalable Architecture** with Virtual Thread integration and asynchronous operations

The key assembly process flows from global configuration down to individual lock instances, with each layer adding specific segments while maintaining consistency and enabling proper semantic isolation between different lock types.
