-- get.lua
-- This script performs a GET operation and stores the result in a response cache.
-- It implements idempotency by checking the response cache first.
--
-- KEYS[1] - The key to get
-- KEYS[2] - The response cache key
-- 
-- ARGV[1] - The request UUID for idempotency
-- ARGV[2] - The response cache TTL in seconds

-- Check if response cache exists first for idempotency
local cached = redis.call('GET', KEYS[2])
if cached and cached ~= "" then
    return cached
elseif cached == "" then
    return nil
end

-- Perform the GET operation
local value = redis.call('GET', KEYS[1])

-- Store the result in the response cache with TTL
if value then
    redis.call('SETEX', KEYS[2], ARGV[2], value)
else
    redis.call('SETEX', KEYS[2], ARGV[2], "")
end

return value
