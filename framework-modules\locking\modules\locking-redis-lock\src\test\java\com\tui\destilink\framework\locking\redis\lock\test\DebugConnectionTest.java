package com.tui.destilink.framework.locking.redis.lock.test;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;

import com.tui.destilink.framework.test.support.redis.annotation.RedisTestSupport;

/**
 * Test to debug the Redis connection factory and native client type.
 */
@SpringBootTest(classes = TestApplication.class)
@RedisTestSupport
public class DebugConnectionTest {

    @Autowired
    private RedisConnectionFactory redisConnectionFactory;

    @Test
    public void debugConnectionFactory() {
        System.out.println("=== DEBUG CONNECTION FACTORY ===");
        System.out.println("RedisConnectionFactory type: " + redisConnectionFactory.getClass().getSimpleName());
        
        if (redisConnectionFactory instanceof LettuceConnectionFactory lettuceFactory) {
            Object nativeClient = lettuceFactory.getNativeClient();
            System.out.println("Native client type: " + nativeClient.getClass().getSimpleName());
            System.out.println("Native client class: " + nativeClient.getClass().getName());
            System.out.println("Is RedisClusterClient? " + (nativeClient instanceof io.lettuce.core.cluster.RedisClusterClient));
            
            System.out.println("Connection factory configuration:");
            System.out.println("- Cluster configuration: " + lettuceFactory.getClusterConfiguration());
            System.out.println("- Standalone configuration: " + lettuceFactory.getStandaloneConfiguration());
            System.out.println("- Sentinel configuration: " + lettuceFactory.getSentinelConfiguration());
            
            if (lettuceFactory.getClusterConfiguration() != null) {
                System.out.println("- Cluster nodes: " + lettuceFactory.getClusterConfiguration().getClusterNodes());
            }
        }
        System.out.println("=== END DEBUG ===");
    }
}
