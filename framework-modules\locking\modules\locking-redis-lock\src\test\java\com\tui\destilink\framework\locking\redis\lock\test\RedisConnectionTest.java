package com.tui.destilink.framework.locking.redis.lock.test;

import io.lettuce.core.RedisURI;
import io.lettuce.core.cluster.RedisClusterClient;
import io.lettuce.core.cluster.api.StatefulRedisClusterConnection;
import io.lettuce.core.cluster.api.sync.RedisAdvancedClusterCommands;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.AfterEach;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Simple test to verify Redis cluster connectivity with authentication.
 */
public class RedisConnectionTest {

    private RedisClusterClient client;
    private StatefulRedisClusterConnection<String, String> connection;

    @AfterEach
    void cleanup() {
        if (connection != null) {
            connection.close();
        }
        if (client != null) {
            client.shutdown();
        }
    }

    @Test
    void testRedisClusterConnection() {
        // Test using RedisURI.Builder
        RedisURI redisURI = RedisURI.Builder.redis("localhost")
                .withPort(6378)
                .withAuthentication("default", "redispassword")
                .build();
        
        client = RedisClusterClient.create(redisURI);
        connection = client.connect();
        RedisAdvancedClusterCommands<String, String> commands = connection.sync();
        
        // Test basic operations
        String result = commands.ping();
        assertThat(result).isEqualTo("PONG");
        
        // Test SET/GET operation
        commands.set("test-key", "test-value");
        String value = commands.get("test-key");
        assertThat(value).isEqualTo("test-value");
        
        // Clean up
        commands.del("test-key");
    }

    @Test
    void testRedisClusterConnectionWithUrlString() {
        // Test using URL string approach
        client = RedisClusterClient.create("redis://default:redispassword@localhost:6378");
        connection = client.connect();
        RedisAdvancedClusterCommands<String, String> commands = connection.sync();
        
        // Test basic operations
        String result = commands.ping();
        assertThat(result).isEqualTo("PONG");
        
        // Test SET/GET operation
        commands.set("test-key-2", "test-value-2");
        String value = commands.get("test-key-2");
        assertThat(value).isEqualTo("test-value-2");
        
        // Clean up
        commands.del("test-key-2");
    }
}
