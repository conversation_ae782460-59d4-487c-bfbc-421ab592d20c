-- pttl.lua
-- This script performs a PTTL operation and stores the result in a response cache.
-- It implements idempotency by checking the response cache first.
--
-- KEYS[1] - The key to check TTL for
-- KEYS[2] - The response cache key
-- 
-- ARGV[1] - The request UUID for idempotency
-- ARGV[2] - The response cache TTL in seconds

-- Check if response cache exists first for idempotency
local cached = redis.call('GET', KEYS[2])
if cached then
    return tonumber(cached)
end

-- Perform the PTTL operation
local ttl = redis.call('PTTL', KEYS[1])

-- Store the result in the response cache with TTL
redis.call('SETEX', KEYS[2], ARGV[2], ttl)

return ttl
