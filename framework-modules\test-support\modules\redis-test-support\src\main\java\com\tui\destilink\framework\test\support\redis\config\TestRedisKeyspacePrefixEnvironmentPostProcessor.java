package com.tui.destilink.framework.test.support.redis.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.env.EnvironmentPostProcessor;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.MapPropertySource;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * Environment post processor that automatically modifies Redis keyspace prefixes
 * for test isolation when the test support framework is active.
 * <p>
 * This processor intercepts the Redis core properties before they are bound to the
 * {@link com.tui.destilink.framework.redis.core.config.RedisCoreProperties} bean
 * and automatically prepends the test unique ID to ensure key isolation.
 * <p>
 * The processor works by:
 * <ul>
 *   <li>Detecting if redis-test-support is active in the environment</li>
 *   <li>Retrieving the unique test ID from the TestConfigProvider</li>
 *   <li>Modifying keyspace prefix properties to include the unique ID</li>
 *   <li>Preserving any custom prefixes configured in application.yaml</li>
 * </ul>
 * <p>
 * Property modification examples:
 * <ul>
 *   <li>application: {@code myapp} → {@code testId:myapp}</li>
 *   <li>distributed: {@code __distributed__} → {@code testId:__distributed__}</li>
 *   <li>Custom from yaml: {@code custom-prefix} → {@code testId:custom-prefix}</li>
 * </ul>
 * <p>
 * The processor runs with high precedence to ensure it modifies properties before
 * other environment processors and before property binding occurs.
 *
 * @see com.tui.destilink.framework.redis.core.config.RedisCoreProperties.KeyspacePrefixes
 */
@Slf4j
@Order(Ordered.HIGHEST_PRECEDENCE + 10)
public class TestRedisKeyspacePrefixEnvironmentPostProcessor implements EnvironmentPostProcessor {

    /** Property source name for test-modified Redis properties */
    private static final String PROPERTY_SOURCE_NAME = "testRedisKeyspacePrefixes";
    
    /** Base property path for Redis core keyspace prefixes */
    private static final String KEYSPACE_PREFIX_BASE = "destilink.fw.redis.core.keyspace-prefixes";
    
    /** Application keyspace prefix property name */
    private static final String APPLICATION_PREFIX_PROPERTY = KEYSPACE_PREFIX_BASE + ".application";
    
    /** Distributed keyspace prefix property name */
    private static final String DISTRIBUTED_PREFIX_PROPERTY = KEYSPACE_PREFIX_BASE + ".distributed";
    
    /** System property key for storing unique test ID */
    private static final String UNIQUE_ID_SYSTEM_PROPERTY = "destilink.test.support.redis.unique-id";

    @Override
    public void postProcessEnvironment(ConfigurableEnvironment environment, SpringApplication application) {
        // Check if we're in a test environment with redis-test-support active
        if (!isRedisTestSupportActive(environment)) {
            return;
        }

        String uniqueId = getUniqueTestId(environment);
        if (!StringUtils.hasText(uniqueId)) {
            log.warn("Redis test support is active but no unique ID found. Keyspace prefixes will not be modified.");
            return;
        }

        // Create modified properties with test prefixes
        Map<String, Object> testProperties = createTestKeyspacePrefixProperties(environment, uniqueId);
        
        if (!testProperties.isEmpty()) {
            // Add the modified properties as a high-priority property source
            MapPropertySource testPropertySource = new MapPropertySource(PROPERTY_SOURCE_NAME, testProperties);
            environment.getPropertySources().addFirst(testPropertySource);
            
            log.debug("Applied test keyspace prefix modifications: {}", testProperties);
        }
    }

    /**
     * Determines if Redis test support is active in the current environment.
     * <p>
     * This method checks for indicators that suggest the redis-test-support module
     * is being used, such as specific property sources or test-related system properties.
     *
     * @param environment The current Spring environment
     * @return true if Redis test support appears to be active
     */
    private boolean isRedisTestSupportActive(ConfigurableEnvironment environment) {
        // Check for test-specific property sources that indicate redis-test-support is active
        return environment.getPropertySources().stream()
                .anyMatch(source -> source.getName().contains("redis-test-support") ||
                                   source.getName().contains("application.test.yml") ||
                                   source.getName().contains("test"));
    }

    /**
     * Retrieves the unique test ID from the environment.
     * <p>
     * The unique ID can be provided through system properties or other mechanisms
     * used by the test support framework.
     *
     * @param environment The current Spring environment
     * @return The unique test ID, or null if not found
     */
    private String getUniqueTestId(ConfigurableEnvironment environment) {
        // Try to get unique ID from system properties first
        String uniqueId = System.getProperty(UNIQUE_ID_SYSTEM_PROPERTY);
        
        if (StringUtils.hasText(uniqueId)) {
            return uniqueId;
        }
        
        // Fallback: try to get from environment properties
        return environment.getProperty("destilink.test.support.redis.unique-id");
    }

    /**
     * Creates a map of modified keyspace prefix properties with the unique test ID prepended.
     * <p>
     * This method preserves any custom keyspace prefixes that may have been configured
     * in application.yaml while ensuring test isolation through unique ID prepending.
     *
     * @param environment The current Spring environment
     * @param uniqueId The unique test identifier
     * @return Map of property names to modified values
     */
    private Map<String, Object> createTestKeyspacePrefixProperties(ConfigurableEnvironment environment, String uniqueId) {
        Map<String, Object> testProperties = new HashMap<>();

        // Get current application prefix (may be custom from application.yaml)
        String currentApplicationPrefix = environment.getProperty(APPLICATION_PREFIX_PROPERTY);
        if (currentApplicationPrefix != null) {
            String modifiedApplicationPrefix = prependUniqueId(uniqueId, currentApplicationPrefix);
            testProperties.put(APPLICATION_PREFIX_PROPERTY, modifiedApplicationPrefix);
        } else {
            // Use default with unique ID if no custom prefix is configured
            testProperties.put(APPLICATION_PREFIX_PROPERTY, uniqueId + ":${spring.application.name}");
        }

        // Get current distributed prefix (may be custom from application.yaml)
        String currentDistributedPrefix = environment.getProperty(DISTRIBUTED_PREFIX_PROPERTY);
        if (currentDistributedPrefix != null) {
            String modifiedDistributedPrefix = prependUniqueId(uniqueId, currentDistributedPrefix);
            testProperties.put(DISTRIBUTED_PREFIX_PROPERTY, modifiedDistributedPrefix);
        } else {
            // Use default with unique ID if no custom prefix is configured
            testProperties.put(DISTRIBUTED_PREFIX_PROPERTY, uniqueId + ":__distributed__");
        }

        return testProperties;
    }

    /**
     * Prepends the unique ID to a keyspace prefix value.
     * <p>
     * The method handles both literal values and placeholder expressions,
     * ensuring that the resulting prefix maintains proper formatting.
     *
     * @param uniqueId The unique test identifier
     * @param originalPrefix The original prefix value (may contain placeholders)
     * @return The prefix with unique ID prepended
     */
    private String prependUniqueId(String uniqueId, String originalPrefix) {
        if (!StringUtils.hasText(originalPrefix)) {
            return uniqueId;
        }
        
        // If the prefix already starts with the unique ID, don't duplicate it
        if (originalPrefix.startsWith(uniqueId + ":")) {
            return originalPrefix;
        }
        
        return uniqueId + ":" + originalPrefix;
    }

    /**
     * Sets the unique test ID in the system properties for use by this processor.
     * <p>
     * This method should be called by the test framework before Spring context initialization
     * to ensure the unique ID is available during environment post-processing.
     *
     * @param uniqueId The unique test identifier
     */
    public static void setUniqueTestId(String uniqueId) {
        if (StringUtils.hasText(uniqueId)) {
            System.setProperty(UNIQUE_ID_SYSTEM_PROPERTY, uniqueId);
        }
    }

    /**
     * Clears the unique test ID from system properties.
     * <p>
     * This method should be called after test execution to clean up system properties.
     */
    public static void clearUniqueTestId() {
        System.clearProperty(UNIQUE_ID_SYSTEM_PROPERTY);
    }
}
