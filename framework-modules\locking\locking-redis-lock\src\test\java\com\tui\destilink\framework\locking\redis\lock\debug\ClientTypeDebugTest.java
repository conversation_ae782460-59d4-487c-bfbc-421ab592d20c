package com.tui.destilink.framework.locking.redis.lock.debug;

import com.tui.destilink.framework.test.support.redis.annotation.RedisTestSupport;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;

/**
 * Debug test to understand what type of Redis client is being created
 * in the single-node cluster setup.
 */
@SpringBootTest
@RedisTestSupport
class ClientTypeDebugTest {

    @Autowired
    private RedisConnectionFactory redisConnectionFactory;

    @Test
    void debugRedisClientType() {
        System.out.println("RedisConnectionFactory type: " + redisConnectionFactory.getClass().getSimpleName());
        
        if (redisConnectionFactory instanceof LettuceConnectionFactory) {
            LettuceConnectionFactory lettuceFactory = (LettuceConnectionFactory) redisConnectionFactory;
            Object nativeClient = lettuceFactory.getNativeClient();
            
            System.out.println("Native client type: " + nativeClient.getClass().getName());
            System.out.println("Native client: " + nativeClient);
            
            // Check if it's a cluster client
            System.out.println("Is RedisClusterClient: " + (nativeClient instanceof io.lettuce.core.cluster.RedisClusterClient));
            System.out.println("Is RedisClient: " + (nativeClient instanceof io.lettuce.core.RedisClient));
            
            // Print all interfaces and superclasses
            System.out.println("All interfaces:");
            for (Class<?> iface : nativeClient.getClass().getInterfaces()) {
                System.out.println("  - " + iface.getName());
            }
            
            System.out.println("Superclasses:");
            Class<?> superClass = nativeClient.getClass().getSuperclass();
            while (superClass != null) {
                System.out.println("  - " + superClass.getName());
                superClass = superClass.getSuperclass();
            }
        }
    }
}
