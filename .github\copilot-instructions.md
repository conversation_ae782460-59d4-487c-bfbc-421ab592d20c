# Destilink Framework AI Coding Assistant Guide

This document provides essential guidelines for AI-powered development within the Destilink Framework. Adhering to these instructions is critical for maintaining code quality, consistency, and stability.

## Framework Architecture Overview

### Core Principles
- **Opinionated Spring Boot Abstraction**: Destilink standardizes and accelerates microservice and cronjob development
- **Multi-Module Maven Structure**: 15+ framework modules with hierarchical dependency management
- **Auto-Configuration First**: Custom Spring Boot auto-configuration pattern with explicit component registration
- **Test-Support Ecosystem**: Comprehensive test modules for all technologies (Redis, PostgreSQL, Keycloak, etc.)
- **Asynchronous-First Design**: Built on Java's `CompletableFuture` and Virtual Threads for non-blocking operations

### Project Structure
```
framework-dependencies-parent/     # Central version management
framework-bom/                    # Bill of Materials for all modules  
framework-build-parent/           # Common build configuration
├── framework-build-parent-ms/    # Microservice build parent
└── framework-build-parent-cronjob/ # Cronjob build parent
framework-modules/                # Core implementation modules (15+)
├── core/                        # Logging, metrics, tracing foundations
├── ms-core/                     # Microservice-specific functionality
├── cronjob-core/               # Cronjob-specific functionality
├── web/                        # Web modules (core, server, security, openapi)
├── redis-core/                 # Redis integration
├── locking/                    # Distributed locking (Redis, ShedLock)
├── aws/                        # AWS service integrations
├── test-support/               # Test framework modules
└── [other modules]
framework-test-applications/      # Integration test applications
utils/                           # Docker Compose development environment
```

### Technology Stack
- **Java 21** with Virtual Threads for async operations
- **Spring Boot 3.4.5** with custom auto-configuration patterns
- **Maven** multi-module project with strict dependency management
- **Redis** for distributed locking and caching
- **Docker Compose** for local development (PostgreSQL, Redis, Keycloak, LocalStack)

### Key Architectural Components

#### Distributed Locking (`locking-redis-lock`)
- **Atomicity**: All Redis operations executed as atomic Lua scripts
- **Idempotency**: Operations managed by `RedisLockOperationsImpl` with `requestUuid`
- **Watchdog**: `LockWatchdog` service automatically extends active lock leases
- **Key Schema**: `<prefix>:<bucketName>:__locks__:<lockType>:{<lockName>}`

#### Build & Test Commands

*   **Full Build**: `mvn clean install`
*   **Run All Tests**: `mvn test`
*   **Run a Single Test**: `mvn test -Dtest=com.tui.destilink.framework.locking.redis.lock.impl.RedisStampedLockTest`
*   **Build Documentation**: `mkdocs build` (uses `mkdocs.yml`)
*   **Start Development Environment**: `docker-compose -f utils/docker-compose.yaml up -d`

## Critical Coding Rules

### Component Registration Rules (MANDATORY)

*   **NO COMPONENT SCANNING**: Modules **MUST NOT** use `@ComponentScan`. This is the most critical rule. All beans must be loaded explicitly via `@Import` statements in `@AutoConfiguration` classes or defined in `@Bean` methods.

*   **Auto-Configuration Pattern**: Every module must follow the exact pattern:
    ```java
    @AutoConfiguration  // REQUIRED - NEVER use @Configuration here
    @EnableConfigurationProperties(ModuleProperties.class)
    @ConditionalOnProperty(prefix = "destilink.fw.module", name = "enabled", 
                          havingValue = "true", matchIfMissing = true)
    public class ModuleAutoConfiguration {
        
        @Bean
        @ConditionalOnMissingBean  // MANDATORY for all service beans
        public ServiceInterface serviceImplementation(ModuleProperties properties) {
            return new ServiceImplementation(properties);
        }
        
        // Component imports - EXPLICIT ONLY
        @Configuration
        @Import({ComponentA.class, ComponentB.class})
        static class ComponentConfiguration { }
    }
    ```

*   **Registration Required**: Every `@AutoConfiguration` class **MUST** be registered in:
    ```
    src/main/resources/META-INF/spring/org.springframework.boot.autoconfigure.AutoConfiguration.imports
    ```

*   **Conditional Beans**: Every `@Bean` definition **MUST** include at least one conditional annotation:
    - `@ConditionalOnProperty` for feature toggles
    - `@ConditionalOnClass` for optional dependencies
    - `@ConditionalOnMissingBean` for overrideable services
    - `@ConditionalOnBean` for dependent features

### Dependency Management Rules

*   **Constructor Injection ONLY**: Use constructor injection with `final` fields for all dependencies. `@Autowired` on fields is forbidden.

*   **Transitive Dependency Verification**: Before adding any non-framework dependency:
    1. Run `mvn dependency:tree -Dverbose` 
    2. Verify the dependency is NOT already available transitively
    3. Document verification in code comments
    4. Only add if confirmed missing from transitive dependencies

*   **Version Management Hierarchy**:
    - `framework-dependencies-parent`: Central version properties for external dependencies
    - `framework-bom`: Provides versions of all framework modules
    - Module POMs: Only specify versions for module-exclusive dependencies
    - Never override parent-managed versions without explicit justification

### Package & Naming Conventions

*   **Packages**: `com.tui.destilink.framework.<module>[.<submodule>]`
*   **Auto-Configuration Classes**: `<Feature>AutoConfiguration` (e.g., `RedisLockAutoConfiguration`)
*   **Test Classes**: Unit tests end with `*Test`, integration tests end with `*IT`
*   **Bean Method Names**: Must match return type camelCase (e.g., `redisTemplate()` for `RedisTemplate`)
*   **Configuration Files**: `1000-<module>-<descriptor>.application.yml` pattern

### Testing Requirements (MANDATORY)

*   **Test-Support Modules**: All tests **MUST** use the provided `test-support` modules:
    ```xml
    <!-- REQUIRED for ALL tests -->
    <dependency>
      <groupId>com.tui.destilink.framework.test-support</groupId>
      <artifactId>test-core</artifactId>
      <scope>test</scope>
    </dependency>
    
    <!-- Technology-specific test support -->
    <dependency>
      <groupId>com.tui.destilink.framework.test-support</groupId>
      <artifactId>redis-test-support</artifactId>
      <scope>test</scope>
    </dependency>
    ```

*   **DO NOT** use standard Spring Boot test dependencies like `spring-boot-starter-test` directly
*   **Integration Tests**: Use `@RedisTestSupport`, `@KeycloakTestSupport`, etc., annotations
*   **Test Isolation**: Use `TestUtils.generateTestClassId()` for isolated test resources
*   **Development Environment**: Use `utils/docker-compose.yaml` for local testing with real services

### Property Configuration

*   **Property Prefix**: `destilink.fw.<module>[.<submodule>].<property-name>`
*   **Naming Style**: kebab-case (e.g., `destilink.fw.web.core.error-responses.use-problem-details`)
*   **Configuration Classes**: Use `@ConfigurationProperties`, `@Validated`, appropriate validation annotations
*   **Defaults**: Always provide sensible defaults with `matchIfMissing = true`

### Error Handling

*   **Web Errors**: Use `@ControllerAdvice` classes returning `ProblemDetail` (RFC 7807)
*   **Framework Exceptions**: Extend `MarkerNestedRuntimeException` or `MarkerNestedException`
*   **Logging**: Use `@Slf4j` with structured context via `AbstractContextDecorator<T>` subclasses

## Module Development Patterns

### Auto-Configuration Template
```java
/**
 * Auto-configuration for [Module] functionality.
 * [Comprehensive JavaDoc required]
 */
@AutoConfiguration
@EnableConfigurationProperties(ModuleProperties.class)
@ConditionalOnProperty(prefix = "destilink.fw.module", name = "enabled", 
                      havingValue = "true", matchIfMissing = true)
public class ModuleAutoConfiguration {

    // ==========================================
    // CORE BEANS - Primary functionality  
    // ==========================================
    
    @Bean
    @ConditionalOnMissingBean
    public ModuleService moduleService(ModuleProperties properties) {
        return new ModuleServiceImpl(properties);
    }
    
    // ==========================================
    // OPTIONAL FEATURES - Conditionally activated
    // ==========================================
    
    @Configuration(proxyBeanMethods = false)
    @ConditionalOnClass(OptionalDependency.class)
    static class OptionalConfiguration {
        
        @Bean
        @ConditionalOnMissingBean
        public OptionalFeature optionalFeature() {
            return new OptionalFeatureImpl();
        }
    }
    
    // ==========================================
    // COMPONENT IMPORTS - Explicitly imported
    // ==========================================
    
    @Configuration
    @Import({ServiceA.class, ServiceB.class})
    static class ComponentConfiguration {
        // Additional @Bean definitions if needed
    }
}
```

### Test-Support Module Pattern
```java
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
@ExtendWith(TechnologyTestExecutionListener.class)
@TestPropertySource(locations = "classpath:1000-technology-test-support.application.test.yml")
public @interface TechnologyTestSupport {
    // Test configuration parameters
}
```

### Property Configuration Pattern
```java
@ConfigurationProperties(prefix = "destilink.fw.module")
@Validated
@Data
public class ModuleProperties {
    
    @NotNull
    private Boolean enabled = true;
    
    @Valid
    @NotNull
    private FeatureConfig feature = new FeatureConfig();
    
    @Data
    public static class FeatureConfig {
        @NotBlank
        private String parameter = "default-value";
        
        @Min(1)
        @Max(3600)
        private Duration timeout = Duration.ofSeconds(30);
    }
}
```

## Absolutely Prohibited Practices

### Critical Violations (Zero Tolerance)
- **Component Scanning**: NEVER use `@ComponentScan` in any form
- **Field Injection**: NEVER use `@Autowired` on fields
- **Unconditional Beans**: NEVER create `@Bean` methods without conditional annotations
- **Direct Test Dependencies**: NEVER use `spring-boot-starter-test` directly
- **Version Overrides**: NEVER override parent-managed dependency versions without justification

### Build & Dependency Violations
- **Transitive Dependencies**: NEVER add dependencies without verifying they're not already available
- **Missing Registration**: NEVER create auto-configuration without registering in `AutoConfiguration.imports`
- **POM Modifications**: NEVER modify top-level POMs directly
- **Test Frameworks**: NEVER bypass test-support modules for framework testing
