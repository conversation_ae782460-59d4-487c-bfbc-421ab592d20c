package com.tui.destilink.framework.locking.redis.lock.impl;

import com.tui.destilink.framework.locking.redis.lock.AsyncLock;
import com.tui.destilink.framework.locking.redis.lock.config.RedisLockProperties;
import com.tui.destilink.framework.locking.redis.lock.impl.RedisReadLock;
import com.tui.destilink.framework.locking.redis.lock.service.LockBucketRegistry;
import com.tui.destilink.framework.locking.redis.lock.service.LockOwnerSupplier;
import com.tui.destilink.framework.locking.redis.lock.service.LockWatchdog;
import com.tui.destilink.framework.locking.redis.lock.service.RedisLockOperations;
import com.tui.destilink.framework.locking.redis.lock.test.TestApplication;
import com.tui.destilink.framework.redis.core.config.RedisCoreProperties;
import com.tui.destilink.framework.test.support.core.util.TestUtils;
import com.tui.destilink.framework.test.support.redis.annotation.RedisTestSupport;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.DirtiesContext;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;

/**
 * Integration tests for {@link RedisReadWriteLock}.
 * <p>
 * Tests the distributed read-write lock implementation ensuring:
 * - Multiple readers can acquire the read lock concurrently
 * - Writers have exclusive access and block readers
 * - Readers block writers
 * - Lock state is properly persisted in Redis
 * - Lock transitions between read and write modes correctly
 * </p>
 */
@Slf4j
@RedisTestSupport(keyspacePrefixes = { "test-locks:","" })
@SpringBootTest(classes = TestApplication.class, webEnvironment = SpringBootTest.WebEnvironment.NONE)
@DirtiesContext(classMode = DirtiesContext.ClassMode.BEFORE_EACH_TEST_METHOD)
public class RedisReadWriteLockIT {

    private static final String UNIQUE_ID = TestUtils.generateTestClassId(RedisReadWriteLockIT.class);

    @Autowired
    private RedisLockOperations redisLockOperations;

    @Autowired
    private LockOwnerSupplier lockOwnerSupplier;

    @Autowired
    private RedisLockProperties redisLockProperties;

    @Autowired
    private ExecutorService virtualThreadExecutor;

    @Autowired
    private LockWatchdog lockWatchdog;

    @Autowired
    private LockBucketRegistry lockBucketRegistry;

    @Autowired
    private RedisCoreProperties redisCoreProperties;

    private RedisReadWriteLock lock;
    private String lockKey;
    
    /**
     * Custom implementation of LockOwnerSupplier for testing with fixed owner IDs
     */
    private static class FixedLockOwnerSupplier implements LockOwnerSupplier {
        private final String ownerId;
        
        public FixedLockOwnerSupplier(String ownerId) {
            this.ownerId = ownerId;
        }
        
        @Override
        public String get() {
            return ownerId;
        }
        
        @Override
        public boolean canUseWatchdog(String lockKey, String lockValue) {
            // For testing purposes, allow watchdog for all locks owned by this supplier
            return lockValue != null && lockValue.contains(ownerId);
        }
    }

    @BeforeEach
    void setUp() {
        // In Redis Cluster, all keys used in a script must hash to the same slot
        // The hash slot is determined by the content between the first { and } in the key
        
        // After analyzing the LockKeyBuilder.buildRedisKeyWithHashTag method and RedisKeyUtils,
        // we need to ensure all keys used in scripts hash to the same Redis Cluster slot
        
        // 1. Create a simple tag value that will be used in Redis key construction
        String tag = "tagtest" + System.currentTimeMillis();
        
        // 2. For Redis Cluster compatibility, we need to ensure all related keys hash to the same slot
        // The key insight is that in the implementation, hash tags are added by the framework
        // We just need to make sure there's only ONE hash tag in the final key
        
        // 3. Use a simple bucket name and lock name that will get hash tags applied consistently
        // The framework handles adding the hash tags via RedisKeyUtils.joinHashTag
        String bucketName = tag + "-bucket";
        String lockName = "lock-" + tag;
        
        System.out.println("Using bucket name '" + bucketName + "' for Redis Cluster testing");
        System.out.println("Using lock name '" + lockName + "'");
        
        // 4. Create the lock using simple bucket and lock names
        // The framework will apply consistent hash tags internally
        lock = lockBucketRegistry.builder(bucketName)
                .lock()
                .readWrite(lockName)
                .build();
        
        // 5. Log the actual generated key for verification
        lockKey = ((RedisReadLock) lock.readLock()).getLockKey();
        System.out.println("Generated lock key: " + lockKey);
        
        // The generated key should have a single hash tag that ensures all related keys
        // used in Lua scripts will hash to the same Redis Cluster slot
    }

    @Test
    @DisplayName("Should create and initialize ReadWriteLock correctly")
    void shouldCreateAndInitializeReadWriteLock() {
        // Debug: Check if keyspace prefixes are properly customized
        log.info("Application prefix: {}", redisCoreProperties.getKeyspacePrefixes().getApplication());
        log.info("Distributed prefix: {}", redisCoreProperties.getKeyspacePrefixes().getDistributed());
        log.info("Application RedisKeyPrefix: {}", redisCoreProperties.getKeyspacePrefixes().getApplicationPrefix());
        log.info("Distributed RedisKeyPrefix: {}", redisCoreProperties.getKeyspacePrefixes().getDistributedPrefix());
        
        // Verify read lock is created
        AsyncLock readLock = lock.readLock();
        assertThat(readLock).isNotNull()
                .isInstanceOf(RedisReadLock.class);
        
        // Cast to the concrete type to access the methods
        RedisReadLock typedReadLock = (RedisReadLock) readLock;
        assertTrue(typedReadLock.isReadLock(), "Read lock should report as read lock");
        assertFalse(typedReadLock.isWriteLock(), "Read lock should not report as write lock");

        // Verify write lock is created
        AsyncLock writeLock = lock.writeLock();
        assertThat(writeLock).isNotNull()
                .isInstanceOf(RedisWriteLock.class);
        
        // Cast to the concrete type to access the methods
        RedisWriteLock typedWriteLock = (RedisWriteLock) writeLock;
        assertTrue(typedWriteLock.isWriteLock(), "Write lock should report as write lock");
        assertFalse(typedWriteLock.isReadLock(), "Write lock should not report as read lock");
    }

    @Test
    @DisplayName("Multiple readers should acquire read lock concurrently")
    void multipleReadersShouldAcquireReadLockConcurrently() {
        // Create multiple read locks with different IDs to simulate different threads/services
        LockOwnerSupplier reader1Supplier = new FixedLockOwnerSupplier("reader1");
        LockOwnerSupplier reader2Supplier = new FixedLockOwnerSupplier("reader2");
        
        // Use the lockKey generated in setUp, which already has correct hash tags
        RedisReadLock readLock1 = new RedisReadLock(
                redisLockOperations,
                reader1Supplier,
                redisLockProperties,
                lockKey,
                1000L,
                50L,
                3,
                virtualThreadExecutor,
                lockWatchdog);
        
        RedisReadLock readLock2 = new RedisReadLock(
                redisLockOperations,
                reader2Supplier,
                redisLockProperties,
                lockKey,
                1000L,
                50L,
                3,
                virtualThreadExecutor,
                lockWatchdog);
        
        // Both readers should be able to acquire the lock simultaneously
        CompletableFuture<Boolean> lockFuture1 = readLock1.tryLockAsync();
        CompletableFuture<Boolean> lockFuture2 = readLock2.tryLockAsync();
        
        boolean reader1Acquired = lockFuture1.join();
        boolean reader2Acquired = lockFuture2.join();
        
        try {
            // Both readers should acquire the lock successfully
            assertTrue(reader1Acquired, "First reader should acquire the lock");
            assertTrue(reader2Acquired, "Second reader should also acquire the lock");
            
            // Verify both locks are actually held
            CompletableFuture<Boolean> isLocked1 = readLock1.isLockedAsync();
            CompletableFuture<Boolean> isLocked2 = readLock2.isLockedAsync();
            
            assertTrue(isLocked1.join(), "First read lock should be held");
            assertTrue(isLocked2.join(), "Second read lock should be held");
        } finally {
            // Clean up
            readLock1.unlockAsync().join();
            readLock2.unlockAsync().join();
        }
    }
    
    @Test
    @DisplayName("Writer should block all readers")
    void writerShouldBlockAllReaders() {
        // First, acquire the write lock
        RedisWriteLock writeLock = (RedisWriteLock) lock.writeLock();
        CompletableFuture<Boolean> writeLockFuture = writeLock.tryLockAsync();
        
        boolean writerAcquired = writeLockFuture.join();
        assertTrue(writerAcquired, "Writer should acquire the lock");
        
        try {
            // Now try to acquire a read lock - should be blocked
            RedisReadLock readLock = (RedisReadLock) lock.readLock();
            CompletableFuture<Boolean> readLockFuture = readLock.tryLockAsync();
            
            boolean readerAcquired = readLockFuture.join();
            assertFalse(readerAcquired, "Reader should be blocked by writer");
            
        } finally {
            // Clean up
            writeLock.unlockAsync().join();
        }
        
        // After releasing the write lock, a reader should be able to acquire the lock
        RedisReadLock readLock = (RedisReadLock) lock.readLock();
        boolean readerAcquiredAfterRelease = readLock.tryLockAsync().join();
        assertTrue(readerAcquiredAfterRelease, "Reader should acquire lock after writer releases it");
        
        // Clean up final read lock
        readLock.unlockAsync().join();
    }

    @Test
    @DisplayName("Reader should block a writer")
    void readerShouldBlockWriter() {
        // First, acquire the read lock
        RedisReadLock readLock = (RedisReadLock) lock.readLock();
        CompletableFuture<Boolean> readLockFuture = readLock.tryLockAsync();
        
        boolean readerAcquired = readLockFuture.join();
        assertTrue(readerAcquired, "Reader should acquire the lock");
        
        try {
            // Now try to acquire a write lock - should be blocked
            RedisWriteLock writeLock = (RedisWriteLock) lock.writeLock();
            CompletableFuture<Boolean> writeLockFuture = writeLock.tryLockAsync();
            
            boolean writerAcquired = writeLockFuture.join();
            assertFalse(writerAcquired, "Writer should be blocked by reader");
            
        } finally {
            // Clean up
            readLock.unlockAsync().join();
        }
        
        // After releasing the read lock, a writer should be able to acquire the lock
        RedisWriteLock writeLock = (RedisWriteLock) lock.writeLock();
        boolean writerAcquiredAfterRelease = writeLock.tryLockAsync().join();
        assertTrue(writerAcquiredAfterRelease, "Writer should acquire lock after reader releases it");
        
        // Clean up final write lock
        writeLock.unlockAsync().join();
    }

    @Test
    @DisplayName("Releasing write lock should allow readers to acquire lock")
    void releasingWriteLockShouldAllowReadersToAcquireLock() {
        // First acquire write lock
        RedisWriteLock writeLock = (RedisWriteLock) lock.writeLock();
        boolean writeAcquired = writeLock.tryLockAsync().join();
        assertTrue(writeAcquired, "Writer should acquire the lock initially");
        
        // Create multiple read locks
        List<RedisReadLock> readLocks = new ArrayList<>();
        List<CompletableFuture<Boolean>> readLockFutures = new ArrayList<>();
        
        // Try to acquire read locks while write lock is held
        for (int i = 0; i < 3; i++) {
            final int idx = i;
            LockOwnerSupplier readerSupplier = new FixedLockOwnerSupplier("reader" + idx);
            
            RedisReadLock readLock = new RedisReadLock(
                    redisLockOperations,
                    readerSupplier,
                    redisLockProperties,
                    lockKey,
                    1000L,
                    50L,
                    3,
                    virtualThreadExecutor,
                    lockWatchdog);
            
            readLocks.add(readLock);
            // Start the acquisition but don't wait for results yet
            readLockFutures.add(readLock.tryLockAsync());
        }
        
        // Immediately check the futures - should all fail
        for (int i = 0; i < readLockFutures.size(); i++) {
            boolean acquired = readLockFutures.get(i).join();
            assertFalse(acquired, "Reader " + i + " should not acquire lock while write lock is held");
        }
        
        // Now release the write lock
        writeLock.unlockAsync().join();
        
        // Now try to acquire read locks again - should succeed
        readLockFutures.clear();
        for (RedisReadLock readLock : readLocks) {
            readLockFutures.add(readLock.tryLockAsync());
        }
        
        // All should succeed now
        for (int i = 0; i < readLockFutures.size(); i++) {
            boolean acquired = readLockFutures.get(i).join();
            assertTrue(acquired, "Reader " + i + " should acquire lock after write lock is released");
        }
        
        // Clean up
        for (RedisReadLock readLock : readLocks) {
            readLock.unlockAsync().join();
        }
    }
    
    @Test
    @DisplayName("Should support Java Lock interface for read and write locks")
    void shouldSupportJavaLockInterface() {
        // Test with read lock
        Lock readLock = lock.readLock();
        
        // Execute lock and catch any exceptions
        try {
            readLock.lock();
        } catch (Exception e) {
            fail("Read lock acquisition should not throw exception: " + e.getMessage());
        }
        
        // Verify read lock is held
        assertTrue(((AsyncLock)readLock).isLockedAsync().join(), "Read lock should be held");
        
        // Now attempt to acquire write lock - should fail with tryLock
        Lock writeLock = lock.writeLock();
        boolean writeLockAcquired = writeLock.tryLock();
        assertFalse(writeLockAcquired, "Write lock should not be acquired while read lock is held");
        
        // Release read lock
        readLock.unlock();
        
        // Now write lock should be acquirable
        try {
            writeLock.lock();
        } catch (Exception e) {
            fail("Write lock acquisition should not throw exception: " + e.getMessage());
        }
        assertTrue(((AsyncLock)writeLock).isLockedAsync().join(), "Write lock should be held");
        
        // Finally release the write lock
        writeLock.unlock();
    }
    
    @Test
    @DisplayName("Should support synchronous tryLock with timeout")
    void shouldSupportSynchronousTryLockWithTimeout() throws InterruptedException {
        // First acquire write lock
        Lock writeLock = lock.writeLock();
        writeLock.lock();
        
        // Try to acquire read lock with timeout - should fail
        Lock readLock = lock.readLock();
        boolean readLockAcquired = readLock.tryLock(100, TimeUnit.MILLISECONDS);
        assertFalse(readLockAcquired, "Read lock should not be acquired within timeout");
        
        // Release write lock
        writeLock.unlock();
        
        // Now read lock should be acquirable
        readLockAcquired = readLock.tryLock(100, TimeUnit.MILLISECONDS);
        assertTrue(readLockAcquired, "Read lock should be acquired after timeout");
        
        // Clean up
        readLock.unlock();
    }
}