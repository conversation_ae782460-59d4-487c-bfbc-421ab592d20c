package com.tui.destilink.framework.locking.redis.lock.test;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.time.Duration;

/**
 * Utility class for validation logic used in Redis lock testing.
 * <p>
 * This class extracts common validation methods from TestRedisLockOperationsImpl
 * to reduce file size and improve maintainability. It provides validation for:
 * - Lock operation parameters
 * - State operation parameters
 * - Read-write lock parameters
 * - General parameter validation
 * </p>
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class TestRedisLockValidationUtils {

    /**
     * Validates parameters for basic lock operations (acquire, release).
     *
     * @param lockKey The lock key
     * @param ownerId The owner ID
     * @param ttl The TTL duration
     * @throws IllegalArgumentException if any parameter is invalid
     */
    public static void validateLockParameters(String lockKey, String ownerId, Duration ttl) {
        validateLockKey(lockKey);
        validateOwnerId(ownerId);
        validateTtl(ttl);
    }

    /**
     * Validates parameters for lock operations without TTL (unlock, check).
     *
     * @param lockKey The lock key
     * @param ownerId The owner ID
     * @throws IllegalArgumentException if any parameter is invalid
     */
    public static void validateLockParameters(String lockKey, String ownerId) {
        validateLockKey(lockKey);
        validateOwnerId(ownerId);
    }

    /**
     * Validates parameters for state lock operations.
     *
     * @param lockKey The lock key
     * @param ownerId The owner ID
     * @param state The state value
     * @param ttl The TTL duration
     * @throws IllegalArgumentException if any parameter is invalid
     */
    public static void validateStateLockParameters(String lockKey, String ownerId, String state, Duration ttl) {
        validateLockParameters(lockKey, ownerId, ttl);
        validateState(state);
    }

    /**
     * Validates parameters for state update operations.
     *
     * @param lockKey The lock key
     * @param ownerId The owner ID
     * @param newState The new state value
     * @throws IllegalArgumentException if any parameter is invalid
     */
    public static void validateStateUpdateParameters(String lockKey, String ownerId, String newState) {
        validateLockParameters(lockKey, ownerId);
        validateState(newState);
    }

    /**
     * Validates parameters for conditional state update operations.
     *
     * @param lockKey The lock key
     * @param ownerId The owner ID
     * @param expectedState The expected current state
     * @param newState The new state value
     * @throws IllegalArgumentException if any parameter is invalid
     */
    public static void validateConditionalStateUpdateParameters(String lockKey, String ownerId, 
                                                               String expectedState, String newState) {
        validateLockParameters(lockKey, ownerId);
        validateState(expectedState);
        validateState(newState);
    }

    /**
     * Validates parameters for read-write lock operations.
     *
     * @param lockKey The lock key
     * @param ownerId The owner ID
     * @param ttl The TTL duration
     * @param lockMode The lock mode
     * @throws IllegalArgumentException if any parameter is invalid
     */
    public static void validateReadWriteLockParameters(String lockKey, String ownerId, Duration ttl, String lockMode) {
        validateLockParameters(lockKey, ownerId, ttl);
        validateLockMode(lockMode);
    }

    /**
     * Validates parameters for stamped lock operations.
     *
     * @param lockKey The lock key
     * @param ownerId The owner ID
     * @param stamp The stamp value
     * @throws IllegalArgumentException if any parameter is invalid
     */
    public static void validateStampedLockParameters(String lockKey, String ownerId, String stamp) {
        validateLockParameters(lockKey, ownerId);
        validateStamp(stamp);
    }

    /**
     * Validates that a lock key is not null or empty.
     *
     * @param lockKey The lock key to validate
     * @throws IllegalArgumentException if the lock key is invalid
     */
    public static void validateLockKey(String lockKey) {
        if (lockKey == null) {
            throw new IllegalArgumentException("lockKey cannot be null");
        }
        if (lockKey.trim().isEmpty()) {
            throw new IllegalArgumentException("lockKey cannot be empty");
        }
    }

    /**
     * Validates that an owner ID is not null or empty.
     *
     * @param ownerId The owner ID to validate
     * @throws IllegalArgumentException if the owner ID is invalid
     */
    public static void validateOwnerId(String ownerId) {
        if (ownerId == null) {
            throw new IllegalArgumentException("ownerId cannot be null");
        }
        if (ownerId.trim().isEmpty()) {
            throw new IllegalArgumentException("ownerId cannot be empty");
        }
    }

    /**
     * Validates that a TTL duration is positive.
     *
     * @param ttl The TTL duration to validate
     * @throws IllegalArgumentException if the TTL is invalid
     */
    public static void validateTtl(Duration ttl) {
        if (ttl == null) {
            throw new IllegalArgumentException("ttl cannot be null");
        }
        if (ttl.isNegative()) {
            throw new IllegalArgumentException("ttl cannot be negative");
        }
        if (ttl.isZero()) {
            throw new IllegalArgumentException("ttl cannot be zero");
        }
    }

    /**
     * Validates that a state value is not null.
     *
     * @param state The state value to validate
     * @throws IllegalArgumentException if the state is invalid
     */
    public static void validateState(String state) {
        if (state == null) {
            throw new IllegalArgumentException("state cannot be null");
        }
        // Note: Empty state is allowed as it may be a valid state value
    }

    /**
     * Validates that a lock mode is not null or empty.
     *
     * @param lockMode The lock mode to validate
     * @throws IllegalArgumentException if the lock mode is invalid
     */
    public static void validateLockMode(String lockMode) {
        if (lockMode == null) {
            throw new IllegalArgumentException("lockMode cannot be null");
        }
        if (lockMode.trim().isEmpty()) {
            throw new IllegalArgumentException("lockMode cannot be empty");
        }
    }

    /**
     * Validates that a stamp value is not null or empty.
     *
     * @param stamp The stamp value to validate
     * @throws IllegalArgumentException if the stamp is invalid
     */
    public static void validateStamp(String stamp) {
        if (stamp == null) {
            throw new IllegalArgumentException("stamp cannot be null");
        }
        if (stamp.trim().isEmpty()) {
            throw new IllegalArgumentException("stamp cannot be empty");
        }
    }

    /**
     * Validates that a timeout duration is not negative.
     *
     * @param timeout The timeout duration to validate
     * @throws IllegalArgumentException if the timeout is invalid
     */
    public static void validateTimeout(Duration timeout) {
        if (timeout == null) {
            throw new IllegalArgumentException("timeout cannot be null");
        }
        if (timeout.isNegative()) {
            throw new IllegalArgumentException("timeout cannot be negative");
        }
        // Note: Zero timeout is allowed for immediate operations
    }

    /**
     * Validates that a response cache key is not null or empty.
     *
     * @param responseCacheKey The response cache key to validate
     * @throws IllegalArgumentException if the response cache key is invalid
     */
    public static void validateResponseCacheKey(String responseCacheKey) {
        if (responseCacheKey == null) {
            throw new IllegalArgumentException("responseCacheKey cannot be null");
        }
        if (responseCacheKey.trim().isEmpty()) {
            throw new IllegalArgumentException("responseCacheKey cannot be empty");
        }
    }

    /**
     * Validates that a request UUID is not null or empty.
     *
     * @param requestUuid The request UUID to validate
     * @throws IllegalArgumentException if the request UUID is invalid
     */
    public static void validateRequestUuid(String requestUuid) {
        if (requestUuid == null) {
            throw new IllegalArgumentException("requestUuid cannot be null");
        }
        if (requestUuid.trim().isEmpty()) {
            throw new IllegalArgumentException("requestUuid cannot be empty");
        }
    }

    /**
     * Validates that a stamped data key is not null or empty.
     *
     * @param stampedDataKey The stamped data key to validate
     * @throws IllegalArgumentException if the stamped data key is invalid
     */
    public static void validateStampedDataKey(String stampedDataKey) {
        if (stampedDataKey == null) {
            throw new IllegalArgumentException("stampedDataKey cannot be null");
        }
        if (stampedDataKey.trim().isEmpty()) {
            throw new IllegalArgumentException("stampedDataKey cannot be empty");
        }
    }

    /**
     * Validates that a lock TTL string is not null or empty and represents a valid number.
     *
     * @param lockTtl The lock TTL string to validate
     * @throws IllegalArgumentException if the lock TTL is invalid
     */
    public static void validateLockTtlString(String lockTtl) {
        if (lockTtl == null) {
            throw new IllegalArgumentException("lockTtl cannot be null");
        }
        if (lockTtl.trim().isEmpty()) {
            throw new IllegalArgumentException("lockTtl cannot be empty");
        }
        
        try {
            long ttlValue = Long.parseLong(lockTtl);
            if (ttlValue <= 0) {
                throw new IllegalArgumentException("lockTtl must be positive");
            }
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("lockTtl must be a valid number: " + lockTtl);
        }
    }
}
