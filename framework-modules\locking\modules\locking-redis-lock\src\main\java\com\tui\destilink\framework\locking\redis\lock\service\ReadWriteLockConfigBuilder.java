package com.tui.destilink.framework.locking.redis.lock.service;

import com.tui.destilink.framework.locking.redis.lock.config.LockBucketConfig;
import com.tui.destilink.framework.locking.redis.lock.impl.RedisReadWriteLock;
import lombok.Getter;
import lombok.AccessLevel;

import java.time.Duration;

/**
 * Builder for configuring and creating Redis-based read-write locks.
 * <p>
 * This builder provides specific configuration for read-write locks.
 * Read-write locks allow multiple concurrent readers or a single writer,
 * providing better performance for read-heavy workloads.
 * </p>
 * <p>
 * Note: This builder does not extend AbstractLockTypeConfigBuilder because
 * RedisReadWriteLock implements AsyncReadWriteLock directly rather than
 * extending AbstractRedisLock.
 * </p>
 */
@Getter(AccessLevel.PROTECTED)
public class ReadWriteLockConfigBuilder {

    private final LockComponentRegistry componentRegistry;
    private final String bucketName;
    private final String lockName;
    private final LockBucketConfig bucketConfig;

    private Duration instanceLeaseTime;
    private Duration instanceRetryInterval;

    /**
     * Creates a new read-write lock config builder.
     *
     * @param componentRegistry The registry of shared lock components
     * @param bucketName        The name of the lock bucket
     * @param lockName          The name of the lock
     * @param bucketConfig      The bucket-level configuration
     */
    public ReadWriteLockConfigBuilder(LockComponentRegistry componentRegistry, String bucketName, String lockName,
            LockBucketConfig bucketConfig) {
        this.componentRegistry = componentRegistry;
        this.bucketName = bucketName;
        this.lockName = lockName;
        this.bucketConfig = bucketConfig;
    }

    /**
     * Sets the lease time for this lock instance.
     *
     * @param leaseTime The lease time duration
     * @return This builder for method chaining
     */
    public ReadWriteLockConfigBuilder withLeaseTime(Duration leaseTime) {
        this.instanceLeaseTime = leaseTime;
        return this;
    }

    /**
     * Sets the retry interval for this lock instance.
     *
     * @param retryInterval The retry interval duration
     * @return This builder for method chaining
     */
    public ReadWriteLockConfigBuilder withRetryInterval(Duration retryInterval) {
        this.instanceRetryInterval = retryInterval;
        return this;
    }

    /**
     * Builds the lock key with the specified lock type segment.
     *
     * @param lockType The lock type segment to include in the key
     * @return The complete lock key
     */
    protected String buildLockKey(String lockType) {
        // Use the LockKeyBuilder utility to generate keys with keyspace prefixes
        return com.tui.destilink.framework.locking.redis.lock.util.LockKeyBuilder
                .forBucket(componentRegistry.getApplicationPrefix(), bucketName)
                .buildLockKey(lockType, lockName);
    }

    /**
     * Gets the effective lease time, using instance-specific value if set,
     * otherwise falling back to bucket or global defaults.
     *
     * @return The effective lease time
     */
    protected Duration getEffectiveLeaseTime() {
        if (instanceLeaseTime != null) {
            return instanceLeaseTime;
        }
        if (bucketConfig.leaseTime() != null) {
            return bucketConfig.leaseTime();
        }
        return componentRegistry.getGlobalRedisLockProperties().getDefaults().getLeaseTime();
    }

    /**
     * Gets the effective retry interval, using instance-specific value if set,
     * otherwise falling back to bucket or global defaults.
     *
     * @return The effective retry interval
     */
    protected Duration getEffectiveRetryInterval() {
        if (instanceRetryInterval != null) {
            return instanceRetryInterval;
        }
        if (bucketConfig.retryInterval() != null) {
            return bucketConfig.retryInterval();
        }
        return componentRegistry.getGlobalRedisLockProperties().getDefaults().getRetryInterval();
    }

    /**
     * Builds and returns a new Redis read-write lock with the configured settings.
     *
     * @return A new RedisReadWriteLock instance
     */
    public RedisReadWriteLock build() {
        // Construct the proper lock key with mandatory lock-type segment
        String lockKey = buildLockKey("readwrite");

        return new RedisReadWriteLock(
                componentRegistry.getRedisLockOperations(),
                componentRegistry.getDefaultLockOwnerSupplier(),
                componentRegistry.getGlobalRedisLockProperties(),
                lockKey,
                getEffectiveLeaseTime().toMillis(),
                getEffectiveRetryInterval().toMillis(),
                componentRegistry.getGlobalRedisLockProperties().getDefaults().getMaxRetries(),
                componentRegistry.getVirtualThreadExecutor(),
                componentRegistry.getLockWatchdog());
    }
}
