-- hget.lua
-- This script performs a HGET operation and stores the result in a response cache.
-- It implements idempotency by checking the response cache first.
--
-- KEYS[1] - The hash key
-- KEYS[2] - The response cache key
-- 
-- ARGV[1] - The request UUID for idempotency
-- ARGV[2] - The response cache TTL in seconds
-- ARGV[3] - The field to get from the hash

-- Check if response cache exists first for idempotency
local cached = redis.call('GET', KEYS[2])
if cached then
    return cached
end

-- Perform the HGET operation
local result = redis.call('HGET', KEYS[1], ARGV[3])

-- Store the result in the response cache with TTL
if result then
    redis.call('SETEX', KEYS[2], ARGV[2], result)
else
    redis.call('SETEX', KEYS[2], ARGV[2], "")
end

return result
