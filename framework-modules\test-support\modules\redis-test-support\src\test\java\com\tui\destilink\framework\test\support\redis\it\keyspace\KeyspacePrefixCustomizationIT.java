package com.tui.destilink.framework.test.support.redis.it.keyspace;

import com.tui.destilink.framework.redis.core.config.RedisCoreProperties;
import com.tui.destilink.framework.test.support.redis.annotation.RedisTestSupport;
import com.tui.destilink.framework.test.support.redis.config.TestConfigProvider;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Integration test verifying that Redis keyspace prefixes are automatically
 * customized with the test unique ID for proper test isolation.
 * <p>
 * This test validates that:
 * <ul>
 *   <li>The {@link com.tui.destilink.framework.test.support.redis.config.TestRedisKeyspacePrefixCustomizer} 
 *       automatically modifies Redis core properties</li>
 *   <li>Both application and distributed prefixes include the unique test ID</li>
 *   <li>The original prefix values are preserved but prepended with the unique ID</li>
 *   <li>Custom prefixes from application.yaml are properly handled</li>
 * </ul>
 */
@Slf4j
@SpringBootTest
@RedisTestSupport
class KeyspacePrefixCustomizationIT {

    @Autowired
    private RedisCoreProperties redisCoreProperties;

    @Autowired
    private TestConfigProvider testConfigProvider;

    /**
     * Tests that default keyspace prefixes are automatically customized with the unique test ID.
     * <p>
     * Verifies that both application and distributed prefixes are modified to include
     * the unique test ID while preserving the original prefix structure.
     */
    @Test
    void testDefaultKeyspacePrefixCustomization() {
        String uniqueId = testConfigProvider.getUniqueId();
        log.info("Testing keyspace prefix customization with unique ID: {}", uniqueId);

        RedisCoreProperties.KeyspacePrefixes keyspacePrefixes = redisCoreProperties.getKeyspacePrefixes();

        // Verify application prefix includes unique ID
        String applicationPrefix = keyspacePrefixes.getApplication();
        assertThat(applicationPrefix)
                .as("Application prefix should start with unique ID")
                .startsWith(uniqueId + ":");

        // Verify distributed prefix includes unique ID
        String distributedPrefix = keyspacePrefixes.getDistributed();
        assertThat(distributedPrefix)
                .as("Distributed prefix should start with unique ID")
                .startsWith(uniqueId + ":");

        // Verify that the RedisKeyPrefix objects are also properly updated
        assertThat(keyspacePrefixes.getApplicationPrefix().toString())
                .as("Application RedisKeyPrefix should reflect the customized value")
                .startsWith(uniqueId + ":");

        assertThat(keyspacePrefixes.getDistributedPrefix().toString())
                .as("Distributed RedisKeyPrefix should reflect the customized value")
                .startsWith(uniqueId + ":");

        log.info("Keyspace prefix customization verified: application={}, distributed={}", 
                applicationPrefix, distributedPrefix);
    }

    /**
     * Tests that the unique ID is unique across different test instances.
     * <p>
     * This test helps ensure that concurrent test executions don't interfere
     * with each other by having the same keyspace prefixes.
     */
    @Test
    void testUniqueIdUniqueness() {
        String uniqueId = testConfigProvider.getUniqueId();
        
        assertThat(uniqueId)
                .as("Unique ID should not be null or empty")
                .isNotBlank();

        // The unique ID should contain some time-based or random component
        // This is a basic check - the actual uniqueness is tested by running multiple tests
        assertThat(uniqueId.length())
                .as("Unique ID should have reasonable length for uniqueness")
                .isGreaterThan(5);

        log.info("Unique ID verified: {}", uniqueId);
    }
}
