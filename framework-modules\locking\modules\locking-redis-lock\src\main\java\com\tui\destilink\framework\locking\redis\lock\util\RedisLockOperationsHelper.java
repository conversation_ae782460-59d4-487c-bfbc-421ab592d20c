package com.tui.destilink.framework.locking.redis.lock.util;

import com.tui.destilink.framework.locking.redis.lock.config.RedisLockProperties;
import com.tui.destilink.framework.redis.core.cluster.ClusterCommandExecutor;
import com.tui.destilink.framework.redis.core.script.ImmutableLettuceScript;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

/**
 * Helper utility class for Redis lock operations.
 * <p>
 * This class extracts common functionality from RedisLockOperationsImpl to reduce
 * file size and improve maintainability. It provides utilities for:
 * - Request UUID generation
 * - Key array construction
 * - Script execution with proper key composition
 * - Response cache TTL management
 * </p>
 */
@Slf4j
@RequiredArgsConstructor
public class RedisLockOperationsHelper {

    private final ClusterCommandExecutor clusterCommandExecutor;
    private final RedisLockProperties properties;

    /**
     * Generates a unique UUID for request idempotency.
     * This centralizes UUID generation for all lock operations.
     *
     * @return A unique UUID string for the current operation
     */
    public String generateRequestUuid() {
        return UUID.randomUUID().toString();
    }

    /**
     * Gets the response cache TTL in seconds as a string.
     *
     * @return The response cache TTL in seconds
     */
    public String getResponseCacheTtlSeconds() {
        return String.valueOf(properties.getResponseCacheTtl().getSeconds());
    }

    /**
     * Constructs a standard keys array for basic lock operations.
     * Order: [responseCacheKey, lockKey, lockDataKey]
     *
     * @param lockKey The primary lock key
     * @param requestUuid The request UUID for idempotency
     * @return Array of keys for Lua script execution
     */
    public String[] buildBasicLockKeys(String lockKey, String requestUuid) {
        String lockDataKey = RedisKeyComposer.composeLockDataKey(lockKey);
        String responseCacheKey = RedisKeyComposer.composeResponseCacheKey(lockKey, requestUuid);
        
        return new String[] {
            responseCacheKey,    // KEYS[1] - For idempotency
            lockKey,            // KEYS[2] - Primary lock
            lockDataKey         // KEYS[3] - Lock metadata
        };
    }

    /**
     * Constructs a keys array for unlock operations.
     * Order: [responseCacheKey, lockKey, lockDataKey, unlockChannelKey]
     *
     * @param lockKey The primary lock key
     * @param requestUuid The request UUID for idempotency
     * @return Array of keys for unlock Lua script execution
     */
    public String[] buildUnlockKeys(String lockKey, String requestUuid) {
        String lockDataKey = RedisKeyComposer.composeLockDataKey(lockKey);
        String responseCacheKey = RedisKeyComposer.composeResponseCacheKey(lockKey, requestUuid);
        String unlockChannelKey = RedisKeyComposer.composeUnlockChannelKey(lockKey);
        
        return new String[] {
            responseCacheKey,    // KEYS[1] - For idempotency
            lockKey,            // KEYS[2] - Primary lock
            lockDataKey,        // KEYS[3] - Lock metadata
            unlockChannelKey    // KEYS[4] - Unlock notification channel
        };
    }

    /**
     * Constructs a keys array for read-write lock operations.
     * Order: [responseCacheKey, lockKey, lockDataKey, readWriteTtlKey]
     *
     * @param lockKey The primary lock key
     * @param requestUuid The request UUID for idempotency
     * @param readerId The reader ID (for read operations)
     * @return Array of keys for read-write Lua script execution
     */
    public String[] buildReadWriteLockKeys(String lockKey, String requestUuid, String readerId) {
        String lockDataKey = RedisKeyComposer.composeLockDataKey(lockKey);
        String responseCacheKey = RedisKeyComposer.composeResponseCacheKey(lockKey, requestUuid);
        String readWriteTtlKey = RedisKeyComposer.composeReadWriteTtlKey(lockKey, readerId);
        
        return new String[] {
            responseCacheKey,    // KEYS[1] - For idempotency
            lockKey,            // KEYS[2] - Primary lock
            lockDataKey,        // KEYS[3] - Lock metadata
            readWriteTtlKey     // KEYS[4] - Individual read lock TTL
        };
    }

    /**
     * Constructs a keys array for state lock operations.
     * Order: [responseCacheKey, lockKey, lockDataKey, stateKey]
     *
     * @param lockKey The primary lock key
     * @param requestUuid The request UUID for idempotency
     * @return Array of keys for state lock Lua script execution
     */
    public String[] buildStateLockKeys(String lockKey, String requestUuid) {
        String lockDataKey = RedisKeyComposer.composeLockDataKey(lockKey);
        String responseCacheKey = RedisKeyComposer.composeResponseCacheKey(lockKey, requestUuid);
        String stateKey = RedisKeyComposer.composeStateKey(lockKey);
        
        return new String[] {
            responseCacheKey,    // KEYS[1] - For idempotency
            lockKey,            // KEYS[2] - Primary lock
            lockDataKey,        // KEYS[3] - Lock metadata
            stateKey            // KEYS[4] - State data
        };
    }

    /**
     * Constructs a keys array for simple operations (like TTL check).
     * Order: [lockKey, responseCacheKey]
     *
     * @param lockKey The primary lock key
     * @param requestUuid The request UUID for idempotency
     * @return Array of keys for simple operation Lua script execution
     */
    public String[] buildSimpleOperationKeys(String lockKey, String requestUuid) {
        String responseCacheKey = RedisKeyComposer.composeResponseCacheKey(lockKey, requestUuid);
        
        return new String[] {
            lockKey,            // KEYS[1] - Primary lock
            responseCacheKey    // KEYS[2] - For idempotency
        };
    }

    /**
     * Constructs a keys array for operation-specific caching.
     * Order: [targetKey, operationCacheKey]
     *
     * @param targetKey The target key for the operation
     * @param operation The operation name
     * @param requestUuid The request UUID for idempotency
     * @return Array of keys for operation-specific caching
     */
    public String[] buildOperationCacheKeys(String targetKey, String operation, String requestUuid) {
        String operationCacheKey = RedisKeyComposer.composeOperationCacheKey(targetKey, operation, requestUuid);
        
        return new String[] {
            targetKey,          // KEYS[1] - Target key
            operationCacheKey   // KEYS[2] - Operation cache key
        };
    }

    /**
     * Constructs standard arguments array for basic lock operations.
     * Order: [requestUuid, responseCacheTtl, ownerId, lockTtl, lockType]
     *
     * @param requestUuid The request UUID
     * @param ownerId The lock owner ID
     * @param lockTtl The lock TTL in milliseconds
     * @param lockType The lock type
     * @return Array of arguments for Lua script execution
     */
    public String[] buildBasicLockArgs(String requestUuid, String ownerId, long lockTtl, String lockType) {
        return new String[] {
            requestUuid,                    // ARGV[1] - Request UUID
            getResponseCacheTtlSeconds(),   // ARGV[2] - Cache TTL
            ownerId,                        // ARGV[3] - Lock owner
            String.valueOf(lockTtl),        // ARGV[4] - Lock TTL
            lockType                        // ARGV[5] - Lock type
        };
    }

    /**
     * Constructs arguments array for simple operations.
     * Order: [requestUuid, responseCacheTtl]
     *
     * @param requestUuid The request UUID
     * @return Array of arguments for simple operation Lua script execution
     */
    public String[] buildSimpleOperationArgs(String requestUuid) {
        return new String[] {
            requestUuid,                    // ARGV[1] - Request UUID
            getResponseCacheTtlSeconds()    // ARGV[2] - Cache TTL
        };
    }

    /**
     * Executes a Lua script with the provided keys and arguments.
     *
     * @param lockKey The primary lock key (for routing)
     * @param script The Lua script to execute
     * @param keys The keys array
     * @param args The arguments array
     * @param <T> The return type
     * @return CompletableFuture with the script result
     */
    public <T> CompletableFuture<T> executeScript(String lockKey, ImmutableLettuceScript<T> script, 
                                                  String[] keys, String[] args) {
        log.debug("Executing script for lockKey: {}, keys: {}, args: {}", lockKey, keys.length, args.length);
        
        return clusterCommandExecutor.executeScript(lockKey, script, keys, args);
    }

    /**
     * Validates lock operation parameters.
     *
     * @param lockKey The lock key
     * @param ownerId The owner ID
     * @param ttl The TTL duration
     * @throws IllegalArgumentException if any parameter is invalid
     */
    public void validateLockParameters(String lockKey, String ownerId, Duration ttl) {
        if (lockKey == null || lockKey.trim().isEmpty()) {
            throw new IllegalArgumentException("Lock key cannot be null or empty");
        }
        if (ownerId == null || ownerId.trim().isEmpty()) {
            throw new IllegalArgumentException("Owner ID cannot be null or empty");
        }
        if (ttl == null || ttl.isNegative() || ttl.isZero()) {
            throw new IllegalArgumentException("TTL must be positive");
        }
    }

    /**
     * Validates unlock operation parameters.
     *
     * @param lockKey The lock key
     * @param ownerId The owner ID
     * @throws IllegalArgumentException if any parameter is invalid
     */
    public void validateUnlockParameters(String lockKey, String ownerId) {
        if (lockKey == null || lockKey.trim().isEmpty()) {
            throw new IllegalArgumentException("Lock key cannot be null or empty");
        }
        if (ownerId == null || ownerId.trim().isEmpty()) {
            throw new IllegalArgumentException("Owner ID cannot be null or empty");
        }
    }
}
