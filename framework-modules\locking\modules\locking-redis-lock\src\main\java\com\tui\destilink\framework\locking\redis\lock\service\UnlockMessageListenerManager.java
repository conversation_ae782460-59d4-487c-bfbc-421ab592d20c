package com.tui.destilink.framework.locking.redis.lock.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.listener.PatternTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.listener.Topic;

import jakarta.annotation.PreDestroy;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executor;

@Slf4j
public class UnlockMessageListenerManager {
    private final RedisMessageListenerContainer listenerContainer;
    private final Executor unlockMessageExecutor;

    /**
     * Map of bucket names to their corresponding unlock message listeners.
     */
    private final Map<String, UnlockMessageListener> bucketListeners = new ConcurrentHashMap<>();

    /**
     * Creates a new UnlockMessageListenerManager.
     *
     * @param listenerContainer     The Redis message listener container
     * @param unlockMessageExecutor The executor for processing unlock messages
     */
    public UnlockMessageListenerManager(RedisMessageListenerContainer listenerContainer,
            Executor unlockMessageExecutor) {
        this.listenerContainer = listenerContainer;
        this.unlockMessageExecutor = unlockMessageExecutor;
    }

    /**
     * Gets or creates an {@link UnlockMessageListener} for the specified bucket.
     * <p>
     * If a listener for the bucket already exists, it is returned. Otherwise, a new
     * listener is created, registered with the Redis message listener container,
     * and
     * returned.
     * </p>
     *
     * @param bucketName The name of the Redis bucket
     * @param keyPrefix  The Redis key prefix for the bucket
     * @return The unlock message listener for the bucket
     */
    public UnlockMessageListener getOrCreateListenerForBucket(String bucketName, String keyPrefix) {
        return bucketListeners.computeIfAbsent(bucketName, bucket -> {
            String channelPattern = buildChannelPattern(keyPrefix, bucketName);
            UnlockMessageListener listener = new UnlockMessageListener(bucketName, channelPattern,
                    unlockMessageExecutor);

            // Register the listener with the Redis message listener container
            Topic topic = new PatternTopic(channelPattern);
            listenerContainer.addMessageListener(listener, topic);

            log.debug("Registered unlock message listener for bucket '{}' with channel pattern '{}'",
                    bucketName, channelPattern);

            return listener;
        });
    }

    /**
     * Builds the Redis channel pattern for unlock notifications.
     * <p>
     * The pattern format is: {@code <prefix><bucketName>:__unlock_channels__:*}
     * where prefix already includes the trailing colon if needed.
     * </p>
     *
     * @param keyPrefix  The Redis key prefix (may be empty or already include trailing colon)
     * @param bucketName The name of the Redis bucket
     * @return The channel pattern
     */
    private String buildChannelPattern(String keyPrefix, String bucketName) {
        // Handle empty prefix case
        if (keyPrefix == null || keyPrefix.isEmpty()) {
            return bucketName + ":__unlock_channels__:*";
        }

        // If prefix already ends with colon, don't add another one
        if (keyPrefix.endsWith(":")) {
            return keyPrefix + bucketName + ":__unlock_channels__:*";
        }

        // Add colon between prefix and bucket name
        return keyPrefix + ":" + bucketName + ":__unlock_channels__:*";
    }

    /**
     * Gets the channel name for a specific lock.
     * <p>
     * The channel format is:
     * {@code <prefix>:<bucketName>:__unlock_channels__:{<lockName>}}
     * </p>
     *
     * @param keyPrefix  The Redis key prefix
     * @param bucketName The name of the Redis bucket
     * @param lockName   The name of the lock
     * @return The channel name
     */
    public String getChannelForLock(String keyPrefix, String bucketName, String lockName) {
        return String.format("%s:%s:__unlock_channels__:{%s}", keyPrefix, bucketName, lockName);
    }

    /**
     * Gets a {@link LockSemaphoreHolder} for the specified lock, creating it if
     * necessary.
     * <p>
     * This method delegates to the appropriate {@link UnlockMessageListener} for
     * the bucket.
     * </p>
     *
     * @param bucketName The name of the Redis bucket
     * @param keyPrefix  The Redis key prefix for the bucket
     * @param lockName   The name of the lock
     * @return The lock semaphore holder
     */
    public LockSemaphoreHolder getOrCreateSemaphoreHolder(String bucketName, String keyPrefix, String lockName) {
        UnlockMessageListener listener = getOrCreateListenerForBucket(bucketName, keyPrefix);
        LockSemaphoreHolder semaphoreHolder = new LockSemaphoreHolder();
        return listener.registerLockSemaphoreHolder(lockName, semaphoreHolder);
    }

    /**
     * Removes a {@link LockSemaphoreHolder} for the specified lock.
     * <p>
     * This method delegates to the appropriate {@link UnlockMessageListener} for
     * the bucket.
     * </p>
     *
     * @param bucketName The name of the Redis bucket
     * @param keyPrefix  The Redis key prefix for the bucket
     * @param lockName   The name of the lock
     * @return The removed lock semaphore holder, or null if none was registered
     */
    public LockSemaphoreHolder removeSemaphoreHolder(String bucketName, String keyPrefix, String lockName) {
        UnlockMessageListener listener = bucketListeners.get(bucketName);
        if (listener != null) {
            return listener.unregisterLockSemaphoreHolder(lockName);
        }
        return null;
    }

    /**
     * Cleans up resources during application shutdown.
     * <p>
     * This method unregisters all listeners from the Redis message listener
     * container.
     * </p>
     */
    @PreDestroy
    public void shutdown() {
        log.info("Shutting down UnlockMessageListenerManager, unregistering {} listeners", bucketListeners.size());

        // Unregister all listeners from the Redis message listener container
        bucketListeners.forEach((bucketName, listener) -> {
            try {
                listenerContainer.removeMessageListener(listener);
                log.debug("Unregistered unlock message listener for bucket '{}'", bucketName);
            } catch (Exception e) {
                log.warn("Error unregistering unlock message listener for bucket '{}'", bucketName, e);
            }
        });

        // Clear the map of listeners
        bucketListeners.clear();
    }
}