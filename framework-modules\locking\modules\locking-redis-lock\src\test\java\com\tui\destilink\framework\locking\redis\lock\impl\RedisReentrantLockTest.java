package com.tui.destilink.framework.locking.redis.lock.impl;

import com.tui.destilink.framework.locking.redis.lock.AsyncLock;
import com.tui.destilink.framework.locking.redis.lock.config.RedisLockProperties;
import com.tui.destilink.framework.locking.redis.lock.service.LockOwnerSupplier;
import com.tui.destilink.framework.locking.redis.lock.service.LockWatchdog;
import com.tui.destilink.framework.locking.redis.lock.service.RedisLockOperations;
import com.tui.destilink.framework.locking.redis.lock.test.TestApplication;
import com.tui.destilink.framework.test.support.core.util.TestUtils;
import com.tui.destilink.framework.test.support.redis.annotation.RedisTestSupport;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.DirtiesContext;

import java.time.Duration;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;

import static org.assertj.core.api.Assertions.*;

/**
 * Comprehensive test suite for {@link RedisReentrantLock}.
 * <p>
 * Tests the distributed reentrant lock implementation ensuring:
 * - Lock functionality works correctly
 * - Reentrancy is supported and managed in Redis
 * - Lock state is properly persisted across application instances
 * - Async-first design works correctly
 * - Compatibility with standard {@link java.util.concurrent.locks.Lock}
 * interface
 * - Error handling and edge cases
 * </p>
 */
@RedisTestSupport(keyspacePrefixes = { "test-locks:" })
@SpringBootTest(classes = TestApplication.class, webEnvironment = SpringBootTest.WebEnvironment.NONE)
@DirtiesContext(classMode = DirtiesContext.ClassMode.BEFORE_EACH_TEST_METHOD)
class RedisReentrantLockTest {

        private static final String UNIQUE_ID = TestUtils.generateTestClassId(RedisReentrantLockTest.class);

        @Autowired
        private RedisLockOperations redisLockOperations;

        @Autowired
        private LockOwnerSupplier lockOwnerSupplier;

        @Autowired
        private RedisLockProperties redisLockProperties;

        @Autowired
        private ExecutorService virtualThreadExecutor;

        @Autowired
        private LockWatchdog lockWatchdog;

        @Autowired
        private com.tui.destilink.framework.locking.redis.lock.test.TestLockKeyUtils testLockKeyUtils;

        private RedisReentrantLock lock;
        private String lockKey;

    @BeforeEach
    void setUp() {
            // Use proper lock key generation with correct schema and application prefix
            lockKey = testLockKeyUtils.createReentrantLockKey("test-locks", UNIQUE_ID, "reentrant-test");
            System.out.println("Generated lock key: " + lockKey);

            // Clean up any existing keys to avoid WRONGTYPE errors
            // We'll let the test framework handle cleanup between tests

            lock = new RedisReentrantLock(
                            redisLockOperations,
                            lockOwnerSupplier,
                            redisLockProperties,
                            lockKey,
                            virtualThreadExecutor,
                            lockWatchdog);
    }

        @Test
        @DisplayName("Should create lightweight, stateless lock instance")
        void shouldCreateLightweightLock() {
                assertThat(lock).isNotNull();
                assertThat(lock.getLockKey()).isEqualTo(lockKey);
                assertThat(lock.getLockType()).isEqualTo("RedisReentrantLock");

                // Verify it implements both AsyncLock and Lock interfaces
                assertThat(lock).isInstanceOf(AsyncLock.class);
                assertThat(lock).isInstanceOf(Lock.class);
        }

        @Test
        @DisplayName("Should acquire and release lock asynchronously")
        void shouldAcquireAndReleaseLockAsync() {
                CompletableFuture<Void> lockFuture = lock.lockAsync();

                assertThat(lockFuture).succeedsWithin(Duration.ofSeconds(5));

                CompletableFuture<Boolean> isLockedFuture = lock.isLockedAsync();
                assertThat(isLockedFuture).succeedsWithin(Duration.ofSeconds(5))
                                .isEqualTo(true);

                CompletableFuture<Void> unlockFuture = lock.unlockAsync();
                assertThat(unlockFuture).succeedsWithin(Duration.ofSeconds(5));

                CompletableFuture<Boolean> isUnlockedFuture = lock.isLockedAsync();
                assertThat(isUnlockedFuture).succeedsWithin(Duration.ofSeconds(5))
                                .isEqualTo(false);
        }

        @Test
        @DisplayName("Should try lock asynchronously")
        void shouldTryLockAsync() {
                CompletableFuture<Boolean> tryLockFuture = lock.tryLockAsync();

                assertThat(tryLockFuture).succeedsWithin(Duration.ofSeconds(5))
                                .isEqualTo(true);

                // Clean up
                lock.unlockAsync().join();
        }

        @Test
        @DisplayName("Should try lock with timeout asynchronously")
        void shouldTryLockWithTimeoutAsync() {
                CompletableFuture<Boolean> tryLockFuture = lock.tryLockAsync(1, TimeUnit.SECONDS);

                assertThat(tryLockFuture).succeedsWithin(Duration.ofSeconds(5))
                                .isEqualTo(true);

                // Clean up
                lock.unlockAsync().join();
        }

        @Test
        @DisplayName("Should work with synchronous Lock interface")
        void shouldWorkWithSynchronousLockInterface() {
                // Test basic lock/unlock
                assertThatCode(() -> lock.lock()).doesNotThrowAnyException();

                // Verify lock is held
                CompletableFuture<Boolean> isLockedFuture = lock.isLockedAsync();
                assertThat(isLockedFuture).succeedsWithin(Duration.ofSeconds(5))
                                .isEqualTo(true);

                assertThatCode(() -> lock.unlock()).doesNotThrowAnyException();

                // Verify lock is released
                CompletableFuture<Boolean> isUnlockedFuture = lock.isLockedAsync();
                assertThat(isUnlockedFuture).succeedsWithin(Duration.ofSeconds(5))
                                .isEqualTo(false);
        }

        @Test
        @DisplayName("Should support tryLock with synchronous interface")
        void shouldSupportSynchronousTryLock() {
                boolean acquired = lock.tryLock();

                assertThat(acquired).isTrue();

                // Clean up
                lock.unlock();
        }

        @Test
        @DisplayName("Should support tryLock with timeout using synchronous interface")
        void shouldSupportSynchronousTryLockWithTimeout() throws InterruptedException {
                boolean acquired = lock.tryLock(1, TimeUnit.SECONDS);

                assertThat(acquired).isTrue();

                // Clean up
                lock.unlock();
        }

        @Test
        @DisplayName("Should throw UnsupportedOperationException for newCondition")
        void shouldThrowUnsupportedOperationExceptionForNewCondition() {
                assertThatThrownBy(() -> lock.newCondition())
                                .isInstanceOf(UnsupportedOperationException.class)
                                .hasMessageContaining("Conditions are not supported");
        }

        @Test
        @DisplayName("Should handle lock expiration (TTL)")
        void shouldHandleLockExpiration() {
                // Create lock with short TTL for testing
                RedisReentrantLock shortTtlLock = new RedisReentrantLock(
                                redisLockOperations,
                                lockOwnerSupplier,
                                redisLockProperties,
                                lockKey,
                                100L, // 100ms TTL
                                50L, // 50ms retry interval
                                3, // 3 max retries
                                virtualThreadExecutor,
                                lockWatchdog
                );

                shortTtlLock.lock();

                // Initial check - should be locked
                CompletableFuture<Boolean> initiallyLocked = shortTtlLock.isLockedAsync();
                assertThat(initiallyLocked).succeedsWithin(Duration.ofSeconds(1))
                                .isEqualTo(true);

                // Wait for TTL to expire and check that lock is gone
                try {
                        Thread.sleep(200); // Wait longer than TTL
                } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                }

                CompletableFuture<Boolean> afterExpiration = shortTtlLock.isLockedAsync();
                assertThat(afterExpiration).succeedsWithin(Duration.ofSeconds(1))
                                .isEqualTo(false);
        }

        @Test
        @DisplayName("Should check if lock is currently held")
        void shouldCheckIfLockIsHeld() {
                // Initially not locked
                CompletableFuture<Boolean> isLockedFuture = lock.isLockedAsync();
                assertThat(isLockedFuture).succeedsWithin(Duration.ofSeconds(5))
                                .isEqualTo(false);

                // Acquire lock
                lock.lockAsync().join();

                // Should now be locked
                CompletableFuture<Boolean> isLockedAfterAcquire = lock.isLockedAsync();
                assertThat(isLockedAfterAcquire).succeedsWithin(Duration.ofSeconds(5))
                                .isEqualTo(true);

                // Release lock
                lock.unlockAsync().join();

                // Should no longer be locked
                CompletableFuture<Boolean> isLockedAfterRelease = lock.isLockedAsync();
                assertThat(isLockedAfterRelease).succeedsWithin(Duration.ofSeconds(5))
                                .isEqualTo(false);
        }
}