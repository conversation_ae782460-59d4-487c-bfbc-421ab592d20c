package com.tui.destilink.framework.test.support.redis.it.keyspace;

import com.tui.destilink.framework.redis.core.config.RedisCoreProperties;
import com.tui.destilink.framework.test.support.redis.annotation.RedisTestSupport;
import com.tui.destilink.framework.test.support.redis.config.TestConfigProvider;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Integration test verifying that custom Redis keyspace prefixes configured in
 * application.yaml are properly preserved while adding test isolation.
 * <p>
 * This test validates that user-defined custom prefixes are respected and
 * the unique test ID is correctly prepended to maintain test isolation.
 */
@Slf4j
@SpringBootTest
@RedisTestSupport
@TestPropertySource(properties = {
        "destilink.fw.redis.core.keyspace-prefixes.application=custom-app-prefix",
        "destilink.fw.redis.core.keyspace-prefixes.distributed=custom-distributed-prefix"
})
class CustomKeyspacePrefixIT {

    @Autowired
    private RedisCoreProperties redisCoreProperties;

    @Autowired
    private TestConfigProvider testConfigProvider;

    /**
     * Tests that custom keyspace prefixes from application.yaml are preserved
     * while being properly prepended with the unique test ID.
     */
    @Test
    void testCustomKeyspacePrefixPreservation() {
        String uniqueId = testConfigProvider.getUniqueId();
        log.info("Testing custom keyspace prefix preservation with unique ID: {}", uniqueId);

        RedisCoreProperties.KeyspacePrefixes keyspacePrefixes = redisCoreProperties.getKeyspacePrefixes();

        // Verify application prefix includes unique ID and preserves custom value
        String applicationPrefix = keyspacePrefixes.getApplication();
        assertThat(applicationPrefix)
                .as("Application prefix should start with unique ID")
                .startsWith(uniqueId + ":");
        assertThat(applicationPrefix)
                .as("Application prefix should preserve custom value")
                .endsWith("custom-app-prefix");
        assertThat(applicationPrefix)
                .as("Application prefix should be in format uniqueId:customPrefix")
                .isEqualTo(uniqueId + ":custom-app-prefix");

        // Verify distributed prefix includes unique ID and preserves custom value
        String distributedPrefix = keyspacePrefixes.getDistributed();
        assertThat(distributedPrefix)
                .as("Distributed prefix should start with unique ID")
                .startsWith(uniqueId + ":");
        assertThat(distributedPrefix)
                .as("Distributed prefix should preserve custom value")
                .endsWith("custom-distributed-prefix");
        assertThat(distributedPrefix)
                .as("Distributed prefix should be in format uniqueId:customPrefix")
                .isEqualTo(uniqueId + ":custom-distributed-prefix");

        // Verify RedisKeyPrefix objects reflect the customization
        assertThat(keyspacePrefixes.getApplicationPrefix().toString())
                .as("Application RedisKeyPrefix should include custom prefix")
                .isEqualTo(uniqueId + ":custom-app-prefix:");

        assertThat(keyspacePrefixes.getDistributedPrefix().toString())
                .as("Distributed RedisKeyPrefix should include custom prefix")
                .isEqualTo(uniqueId + ":custom-distributed-prefix:");

        log.info("Custom keyspace prefix preservation verified: application={}, distributed={}", 
                applicationPrefix, distributedPrefix);
    }

    /**
     * Tests that the customized prefixes can be used to build Redis keys correctly.
     */
    @Test
    void testCustomizedPrefixKeyBuilding() {
        String uniqueId = testConfigProvider.getUniqueId();
        RedisCoreProperties.KeyspacePrefixes keyspacePrefixes = redisCoreProperties.getKeyspacePrefixes();

        // Test building keys with the customized application prefix
        String appKey = keyspacePrefixes.getApplicationPrefix().buildRedisKey("some", "key");
        assertThat(appKey)
                .as("Built key should include the complete custom prefix")
                .startsWith(uniqueId + ":custom-app-prefix:")
                .endsWith("some:key");

        // Test building keys with the customized distributed prefix
        String distributedKey = keyspacePrefixes.getDistributedPrefix().buildRedisKey("distributed", "operation");
        assertThat(distributedKey)
                .as("Built distributed key should include the complete custom prefix")
                .startsWith(uniqueId + ":custom-distributed-prefix:")
                .endsWith("distributed:operation");

        log.info("Key building verified: appKey={}, distributedKey={}", appKey, distributedKey);
    }
}
