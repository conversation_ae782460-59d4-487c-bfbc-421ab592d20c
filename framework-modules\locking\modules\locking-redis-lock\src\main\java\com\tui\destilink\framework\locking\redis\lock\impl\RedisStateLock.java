package com.tui.destilink.framework.locking.redis.lock.impl;

import com.tui.destilink.framework.locking.redis.lock.config.RedisLockProperties;
import com.tui.destilink.framework.locking.redis.lock.exception.LockReleaseException;
import com.tui.destilink.framework.locking.redis.lock.AbstractRedisLock;
import com.tui.destilink.framework.locking.redis.lock.service.LockOwnerSupplier;
import com.tui.destilink.framework.locking.redis.lock.service.LockWatchdog;
import com.tui.destilink.framework.locking.redis.lock.service.RedisLockOperations;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.concurrent.ExecutorService;

/**
 * Redis-based implementation of a state lock.
 * <p>
 * This lock type allows acquiring a lock only if an associated state key
 * holds a specific expected value. It also supports updating the state
 * upon lock acquisition or release.
 * </p>
 * <p>
 * The state is stored in a separate Redis String key, and operations
 * are atomic via Lua scripts.
 * </p>
 */
@Slf4j
public class RedisStateLock extends AbstractRedisLock {

    private static final String LOCK_TYPE = "RedisStateLock";
    private static final String STATE_KEY_SUFFIX = ":state";
    private final String stateKey;
    private final String expectedState;
    private final String newStateOnUnlock;
    private final Duration stateExpiration;
    private final boolean initIfNotExist;

    /**
     * Creates a new Redis-based state lock with the specified key and
     * configuration.
     *
     * @param redisLockOperations Redis lock operations for executing lock commands
     * @param lockOwnerSupplier   Supplier for lock owner identifiers
     * @param properties          Redis lock configuration properties
     * @param lockKey             The unique key identifying this lock in Redis
     * @param lockTtlMillis       The TTL for the lock in milliseconds
     * @param retryIntervalMillis The retry interval in milliseconds
     * @param maxRetries          Maximum number of retry attempts
     * @param virtualThreadExecutor Virtual thread executor for async operations
     * @param watchdog            Lock watchdog for lease extension
     */
    public RedisStateLock(
            RedisLockOperations redisLockOperations,
            LockOwnerSupplier lockOwnerSupplier,
            RedisLockProperties properties,
            String lockKey,
            long lockTtlMillis,
            long retryIntervalMillis,
            int maxRetries,
            ExecutorService virtualThreadExecutor,
            LockWatchdog watchdog) {
        super(redisLockOperations, lockOwnerSupplier, properties, lockKey, lockTtlMillis, retryIntervalMillis, maxRetries, virtualThreadExecutor, watchdog);
        // For now, use default state lock behavior
        this.expectedState = "INITIAL";
        this.newStateOnUnlock = "INITIAL";
        this.stateExpiration = properties.getStateKeyExpiration();
        this.initIfNotExist = true;
        this.stateKey = lockKey + STATE_KEY_SUFFIX;
    }

    @Override
    protected String getLockType() {
        return LOCK_TYPE;
    }

    @Override
    protected CompletableFuture<Boolean> doTryLock(String ownerId, Duration effectiveTimeout) {
        // Use try_state_lock.lua
        return redisLockOperations.tryStateLock(
                lockKey,
                ownerId,
                expectedState, // Initial state if not exists
                Duration.ofMillis(lockTtlMillis),
                initIfNotExist,
                stateExpiration)
                .thenApply(result -> {
                    if (result != null && result) {
                        log.debug("State lock acquired: lockKey={}, ownerId={}, expectedState={}",
                                lockKey, ownerId, expectedState);
                        return true;
                    } else {
                        log.debug(
                                "State lock not acquired, held by another or expired: lockKey={}, ownerId={}, result={}",
                                lockKey, ownerId, result);
                        return false;
                    }
                });
    }

    @Override
    protected CompletableFuture<Void> doUnlock(String ownerId) {
        // Use unlock_state_lock.lua
        return redisLockOperations.unlockStateLock(
                lockKey,
                ownerId,
                newStateOnUnlock,
                stateExpiration,
                "UNLOCK")
                .thenAccept(result -> {
                    if (Boolean.FALSE.equals(result)) {
                        log.warn("Failed to release state lock: lockKey={}, ownerId={}", lockKey, ownerId);
                    }
                })
                .exceptionally(ex -> {
                    log.error("State lock release failed with exception: lockKey={}, ownerId={}", lockKey, ownerId, ex);
                    throw new CompletionException(new LockReleaseException(
                        lockKey,
                        getLockType(),
                        ownerId,
                        "Failed to release state lock due to exception: " + ex.getMessage()));
                });
    }

    /**
     * Asynchronously retrieves the current state associated with this lock.
     *
     * @return a CompletableFuture that completes with the current state string,
     *         or null if the state key does not exist.
     */
    public CompletableFuture<String> getStateAsync() {
        return redisLockOperations.getString(stateKey);
    }

    /**
     * Asynchronously updates the state associated with this lock.
     * This operation will only succeed if the current thread holds the lock.
     *
     * @param newState The new state value to set.
     * @return a CompletableFuture that completes with true if the state was
     *         updated,
     *         false otherwise (e.g., lock not held by current owner).
     */
    public CompletableFuture<Boolean> updateStateAsync(String newState) {
        String ownerId = lockOwnerSupplier.get();
        
        return redisLockOperations.updateState(
                lockKey,
                ownerId,
                newState,
                stateExpiration)
                .thenApply(result -> result != null && result); // Use direct boolean comparison
    }

    /**
     * Asynchronously updates the state associated with this lock, but only if the
     * current state matches the expected value.
     * This operation will only succeed if the current thread holds the lock.
     *
     * @param expectedState The state value that must match the current state.
     * @param newState      The new state value to set.
     * @return a CompletableFuture that completes with true if the state was
     *         updated,
     *         false otherwise (e.g., lock not held by current owner or state
     *         mismatch).
     */
    public CompletableFuture<Boolean> updateStateIfEqualsAsync(String expectedState, String newState) {
        String ownerId = lockOwnerSupplier.get();

        // Use update_state_if_equals.lua script for atomic operation
        return redisLockOperations.updateStateIfEquals(
                lockKey,
                ownerId,
                expectedState,
                newState,
                stateExpiration)
                .thenApply(result -> {
                    if (result != null && result) {
                        log.debug("Successfully updated state atomically: lockKey={}, ownerId={}, expected={}, new={}",
                                lockKey, ownerId, expectedState, newState);
                        return true;
                    } else {
                        log.debug("Failed to update state atomically: lockKey={}, ownerId={}, result={}",
                                lockKey, ownerId, result);
                        return false;
                    }
                });
    }

    @Override
    public boolean isReadLock() {
        return false;
    }

    @Override
    public boolean isWriteLock() {
        return false; // State lock is neither a read nor a write lock in the traditional sense
    }
}