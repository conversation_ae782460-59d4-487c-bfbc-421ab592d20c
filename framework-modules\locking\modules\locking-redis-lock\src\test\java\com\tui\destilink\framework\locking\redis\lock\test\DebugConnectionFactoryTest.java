package com.tui.destilink.framework.locking.redis.lock.test;

import com.tui.destilink.framework.test.support.redis.annotation.RedisTestSupport;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;

@Slf4j
@SpringBootTest(classes = TestApplication.class)
@RedisTestSupport
public class DebugConnectionFactoryTest {
    
    @Autowired
    private RedisConnectionFactory redisConnectionFactory;
    
    @Test
    public void debugConnectionFactory() {
        log.info("Connection factory type: {}", redisConnectionFactory.getClass().getName());
        
        if (redisConnectionFactory instanceof LettuceConnectionFactory) {
            LettuceConnectionFactory lettuceFactory = (LettuceConnectionFactory) redisConnectionFactory;
            Object nativeClient = lettuceFactory.getNativeClient();
            log.info("Native client type: {}", nativeClient.getClass().getName());
            log.info("Native client: {}", nativeClient);
            
            // Check if it's the right type for cluster mode
            log.info("Is RedisClusterClient: {}", nativeClient instanceof io.lettuce.core.cluster.RedisClusterClient);
            log.info("Is RedisClient: {}", nativeClient instanceof io.lettuce.core.RedisClient);
        } else {
            log.info("Connection factory is not LettuceConnectionFactory");
        }
    }
}
