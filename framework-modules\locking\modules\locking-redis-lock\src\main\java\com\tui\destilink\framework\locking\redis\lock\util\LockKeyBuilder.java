package com.tui.destilink.framework.locking.redis.lock.util;

import com.tui.destilink.framework.redis.core.keying.RedisKeyPrefix;
import lombok.RequiredArgsConstructor;

/**
 * Utility class for constructing Redis lock keys with mandatory lock-type segments.
 * <p>
 * This class enforces the key format: {@code <prefix>:<bucketName>:__locks__:<lockType>:{<lockName>}}
 * as specified in the detailed plan for semantic isolation between different lock types.
 * </p>
 * <p>
 * Key features:
 * - Mandatory lock-type segments for semantic isolation
 * - Redis Cluster co-location via hash tags
 * - Consistent key format across all lock operations
 * - Support for related keys (data, response cache, unlock channels)
 * </p>
 *
 * @see <a href="../docs/redis_key_schema.md">Redis Key Schema Documentation</a>
 */
@RequiredArgsConstructor
public class LockKeyBuilder {

    /** The standard delimiter used to separate parts of Redis keys */
    private static final String KEY_DELIMITER = ":";

    /** Fixed segment indicating lock-related keys */
    private static final String LOCKS_NAMESPACE = "__locks__";

    /** Fixed segment for lock buckets */
    private static final String LOCK_BUCKETS_SEGMENT = "__lock_buckets__";

    /** Suffix for lock metadata keys */
    private static final String DATA_SUFFIX = "data";

    /** Namespace for response cache keys */
    private static final String RESPONSE_CACHE_NAMESPACE = "__resp_cache__";

    /** Namespace for unlock notification channels */
    private static final String UNLOCK_CHANNEL_NAMESPACE = "__unlock_channels__";

    /** Namespace for read-write lock TTL keys */
    private static final String READ_WRITE_TTL_NAMESPACE = "__rwttl__";

    private final RedisKeyPrefix applicationPrefix;
    private final String bucketName;

    /**
     * Builds the main lock key with mandatory lock-type segment.
     * Format: {@code <prefix>:<bucketName>:__locks__:<lockType>:{<lockName>}}
     *
     * @param lockType The lock type (e.g., "reentrant", "stamped", "state")
     * @param lockName The name of the lock
     * @return The complete lock key string
     */
    public String buildLockKey(String lockType, String lockName) {
        validateLockType(lockType);
        validateLockName(lockName);
        
        return applicationPrefix
                .append(LOCK_BUCKETS_SEGMENT, bucketName, LOCKS_NAMESPACE, lockType)
                .buildRedisKeyWithHashTag(lockName);
    }

    /**
     * Builds the lock data key for storing metadata.
     * Format: {@code <prefix>:<bucketName>:__locks__:<lockType>:{<lockName>}:data}
     *
     * @param lockType The lock type (e.g., "reentrant", "stamped", "state")
     * @param lockName The name of the lock
     * @return The complete lock data key string
     */
    public String buildLockDataKey(String lockType, String lockName) {
        return buildLockKey(lockType, lockName) + KEY_DELIMITER + DATA_SUFFIX;
    }

    /**
     * Builds a response cache key for idempotency.
     * Format: {@code <prefix>:<bucketName>:__resp_cache__:<lockType>:{<lockName>}:<requestUuid>}
     *
     * @param lockType The lock type (e.g., "reentrant", "stamped", "state")
     * @param lockName The name of the lock
     * @param requestUuid The unique request identifier
     * @return The complete response cache key string
     */
    public String buildResponseCacheKey(String lockType, String lockName, String requestUuid) {
        validateLockType(lockType);
        validateLockName(lockName);
        validateRequestUuid(requestUuid);
        
        return applicationPrefix
                .append(LOCK_BUCKETS_SEGMENT, bucketName, RESPONSE_CACHE_NAMESPACE, lockType)
                .buildRedisKeyWithHashTag(lockName) + KEY_DELIMITER + requestUuid;
    }

    /**
     * Builds an unlock notification channel key.
     * Format: {@code <prefix>:<bucketName>:__unlock_channels__:<lockType>:{<lockName>}}
     *
     * @param lockType The lock type (e.g., "reentrant", "stamped", "state")
     * @param lockName The name of the lock
     * @return The complete unlock channel key string
     */
    public String buildUnlockChannelKey(String lockType, String lockName) {
        validateLockType(lockType);
        validateLockName(lockName);

        return applicationPrefix
                .append(LOCK_BUCKETS_SEGMENT, bucketName, UNLOCK_CHANNEL_NAMESPACE, lockType)
                .buildRedisKeyWithHashTag(lockName);
    }

    /**
     * Builds a read-write lock TTL key for individual reader tracking.
     * Format: {@code <prefix>:<bucketName>:__rwttl__:<lockType>:{<lockName>}:<readerId>}
     *
     * @param lockType The lock type (should be "readwrite")
     * @param lockName The name of the lock
     * @param readerId The unique identifier for the reader
     * @return The complete read-write TTL key string
     */
    public String buildReadWriteTtlKey(String lockType, String lockName, String readerId) {
        validateLockType(lockType);
        validateLockName(lockName);
        validateReaderId(readerId);

        return applicationPrefix
                .append(LOCK_BUCKETS_SEGMENT, bucketName, READ_WRITE_TTL_NAMESPACE, lockType)
                .buildRedisKeyWithHashTag(lockName) + KEY_DELIMITER + readerId;
    }

    /**
     * Builds a state key for state locks.
     * Format: {@code <prefix>:<bucketName>:__locks__:<lockType>:{<lockName>}:state}
     *
     * @param lockType The lock type (should be "state")
     * @param lockName The name of the lock
     * @return The complete state key string
     */
    public String buildStateKey(String lockType, String lockName) {
        validateLockType(lockType);
        validateLockName(lockName);

        return buildLockKey(lockType, lockName) + KEY_DELIMITER + "state";
    }

    /**
     * Creates a new LockKeyBuilder for a specific bucket.
     *
     * @param applicationPrefix The application prefix from configuration
     * @param bucketName The bucket name for grouping locks
     * @return A new LockKeyBuilder instance
     */
    public static LockKeyBuilder forBucket(RedisKeyPrefix applicationPrefix, String bucketName) {
        validateBucketName(bucketName);
        return new LockKeyBuilder(applicationPrefix, bucketName);
    }

    /**
     * Validates that the lock type is not null or empty.
     *
     * @param lockType The lock type to validate
     * @throws IllegalArgumentException if the lock type is invalid
     */
    private static void validateLockType(String lockType) {
        if (lockType == null || lockType.trim().isEmpty()) {
            throw new IllegalArgumentException("Lock type cannot be null or empty");
        }
        if (lockType.contains(KEY_DELIMITER)) {
            throw new IllegalArgumentException("Lock type cannot contain the key delimiter ':'");
        }
    }

    /**
     * Validates that the lock name is not null or empty.
     *
     * @param lockName The lock name to validate
     * @throws IllegalArgumentException if the lock name is invalid
     */
    private static void validateLockName(String lockName) {
        if (lockName == null || lockName.trim().isEmpty()) {
            throw new IllegalArgumentException("Lock name cannot be null or empty");
        }
    }

    /**
     * Validates that the bucket name is not null or empty.
     *
     * @param bucketName The bucket name to validate
     * @throws IllegalArgumentException if the bucket name is invalid
     */
    private static void validateBucketName(String bucketName) {
        if (bucketName == null || bucketName.trim().isEmpty()) {
            throw new IllegalArgumentException("Bucket name cannot be null or empty");
        }
        if (bucketName.contains(KEY_DELIMITER)) {
            throw new IllegalArgumentException("Bucket name cannot contain the key delimiter ':'");
        }
    }

    /**
     * Validates that the request UUID is not null or empty.
     *
     * @param requestUuid The request UUID to validate
     * @throws IllegalArgumentException if the request UUID is invalid
     */
    private static void validateRequestUuid(String requestUuid) {
        if (requestUuid == null || requestUuid.trim().isEmpty()) {
            throw new IllegalArgumentException("Request UUID cannot be null or empty");
        }
    }

    /**
     * Validates that the reader ID is not null or empty.
     *
     * @param readerId The reader ID to validate
     * @throws IllegalArgumentException if the reader ID is invalid
     */
    private static void validateReaderId(String readerId) {
        if (readerId == null || readerId.trim().isEmpty()) {
            throw new IllegalArgumentException("Reader ID cannot be null or empty");
        }
    }

    /**
     * Gets the bucket name for this builder.
     *
     * @return The bucket name
     */
    public String getBucketName() {
        return bucketName;
    }

    /**
     * Gets the application prefix for this builder.
     *
     * @return The application prefix
     */
    public RedisKeyPrefix getApplicationPrefix() {
        return applicationPrefix;
    }
}
