package com.tui.destilink.framework.locking.redis.lock.util;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * Utility class for composing Redis keys according to the documented key schema.
 * <p>
 * This class provides static methods for creating all types of Redis keys used in the
 * locking-redis-lock module, ensuring consistency with the documented format:
 * {@code <prefix>:<bucketName>:<namespace>:<lockType>:{<lockName>}[:<suffix>]}
 * </p>
 * <p>
 * Key features:
 * - Centralized key composition logic
 * - Strict adherence to documented key schema
 * - Support for all key types (lock, data, cache, channels, etc.)
 * - Proper hash tag handling for Redis Cluster compatibility
 * </p>
 * 
 * @see <a href="../docs/redis_key_composition_and_class_hierarchy.md">Redis Key Composition Documentation</a>
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class RedisKeyComposer {

    /** The standard delimiter used to separate parts of Redis keys */
    private static final String KEY_DELIMITER = ":";

    /**
     * Composes a lock data key from a primary lock key.
     * Transforms: {@code prefix:bucket:__locks__:type:{name}} 
     * To: {@code prefix:bucket:__locks__:type:{name}:data}
     *
     * @param lockKey The primary lock key
     * @return The lock data key
     */
    public static String composeLockDataKey(String lockKey) {
        validateLockKey(lockKey);
        return lockKey + KEY_DELIMITER + "data";
    }

    /**
     * Composes a response cache key from a primary lock key and request UUID.
     * Transforms: {@code prefix:bucket:__locks__:type:{name}} 
     * To: {@code prefix:bucket:__resp_cache__:type:{name}:requestUuid}
     *
     * @param lockKey The primary lock key
     * @param requestUuid The unique request identifier
     * @return The response cache key
     */
    public static String composeResponseCacheKey(String lockKey, String requestUuid) {
        validateLockKey(lockKey);
        validateRequestUuid(requestUuid);
        
        String cacheKey = lockKey.replace(":__locks__:", ":__resp_cache__:");
        return cacheKey + KEY_DELIMITER + requestUuid;
    }

    /**
     * Composes an unlock channel key from a primary lock key.
     * Transforms: {@code prefix:bucket:__locks__:type:{name}} 
     * To: {@code prefix:bucket:__unlock_channels__:type:{name}}
     *
     * @param lockKey The primary lock key
     * @return The unlock channel key
     */
    public static String composeUnlockChannelKey(String lockKey) {
        validateLockKey(lockKey);
        return lockKey.replace(":__locks__:", ":__unlock_channels__:");
    }

    /**
     * Composes a read-write TTL key from a primary lock key and reader ID.
     * Transforms: {@code prefix:bucket:__locks__:readwrite:{name}} 
     * To: {@code prefix:bucket:__rwttl__:readwrite:{name}:readerId}
     *
     * @param lockKey The primary lock key (must be readwrite type)
     * @param readerId The unique reader identifier
     * @return The read-write TTL key
     */
    public static String composeReadWriteTtlKey(String lockKey, String readerId) {
        validateLockKey(lockKey);
        validateReaderId(readerId);
        
        if (!lockKey.contains(":__locks__:readwrite:")) {
            throw new IllegalArgumentException("Lock key must be of readwrite type for TTL key composition");
        }
        
        String ttlKey = lockKey.replace(":__locks__:", ":__rwttl__:");
        return ttlKey + KEY_DELIMITER + readerId;
    }

    /**
     * Composes a state key from a primary lock key.
     * Transforms: {@code prefix:bucket:__locks__:state:{name}} 
     * To: {@code prefix:bucket:__locks__:state:{name}:state}
     *
     * @param lockKey The primary lock key (must be state type)
     * @return The state key
     */
    public static String composeStateKey(String lockKey) {
        validateLockKey(lockKey);
        
        if (!lockKey.contains(":__locks__:state:")) {
            throw new IllegalArgumentException("Lock key must be of state type for state key composition");
        }
        
        return lockKey + KEY_DELIMITER + "state";
    }

    /**
     * Composes a generic operation-specific cache key.
     * Format: {@code lockKey:operation:requestUuid}
     *
     * @param lockKey The primary lock key
     * @param operation The operation name (e.g., "pttl", "get", "tryWriteLock")
     * @param requestUuid The unique request identifier
     * @return The operation-specific cache key
     */
    public static String composeOperationCacheKey(String lockKey, String operation, String requestUuid) {
        validateLockKey(lockKey);
        validateOperation(operation);
        validateRequestUuid(requestUuid);
        
        return lockKey + KEY_DELIMITER + operation + KEY_DELIMITER + requestUuid;
    }

    /**
     * Extracts the lock name (without hash tags) from a lock key.
     * Extracts from: {@code prefix:bucket:__locks__:type:{lockName}}
     * Returns: {@code lockName}
     *
     * @param lockKey The primary lock key
     * @return The lock name without hash tags
     */
    public static String extractLockName(String lockKey) {
        validateLockKey(lockKey);
        
        int hashTagStart = lockKey.lastIndexOf("{");
        int hashTagEnd = lockKey.lastIndexOf("}");
        
        if (hashTagStart == -1 || hashTagEnd == -1 || hashTagStart >= hashTagEnd) {
            throw new IllegalArgumentException("Lock key does not contain valid hash tags: " + lockKey);
        }
        
        return lockKey.substring(hashTagStart + 1, hashTagEnd);
    }

    /**
     * Extracts the lock type from a lock key.
     * Extracts from: {@code prefix:bucket:__locks__:lockType:{lockName}}
     * Returns: {@code lockType}
     *
     * @param lockKey The primary lock key
     * @return The lock type
     */
    public static String extractLockType(String lockKey) {
        validateLockKey(lockKey);
        
        String[] parts = lockKey.split(KEY_DELIMITER);
        for (int i = 0; i < parts.length - 1; i++) {
            if ("__locks__".equals(parts[i]) && i + 1 < parts.length) {
                return parts[i + 1];
            }
        }
        
        throw new IllegalArgumentException("Lock key does not contain valid lock type: " + lockKey);
    }

    /**
     * Validates that a lock key follows the expected format.
     *
     * @param lockKey The lock key to validate
     * @throws IllegalArgumentException if the lock key is invalid
     */
    private static void validateLockKey(String lockKey) {
        if (lockKey == null || lockKey.trim().isEmpty()) {
            throw new IllegalArgumentException("Lock key cannot be null or empty");
        }
        
        if (!lockKey.contains(":__locks__:")) {
            throw new IllegalArgumentException("Lock key must contain '__locks__' namespace: " + lockKey);
        }
        
        if (!lockKey.contains("{") || !lockKey.contains("}")) {
            throw new IllegalArgumentException("Lock key must contain hash tags: " + lockKey);
        }
    }

    /**
     * Validates that a request UUID is not null or empty.
     *
     * @param requestUuid The request UUID to validate
     * @throws IllegalArgumentException if the request UUID is invalid
     */
    private static void validateRequestUuid(String requestUuid) {
        if (requestUuid == null || requestUuid.trim().isEmpty()) {
            throw new IllegalArgumentException("Request UUID cannot be null or empty");
        }
    }

    /**
     * Validates that a reader ID is not null or empty.
     *
     * @param readerId The reader ID to validate
     * @throws IllegalArgumentException if the reader ID is invalid
     */
    private static void validateReaderId(String readerId) {
        if (readerId == null || readerId.trim().isEmpty()) {
            throw new IllegalArgumentException("Reader ID cannot be null or empty");
        }
    }

    /**
     * Validates that an operation name is not null or empty.
     *
     * @param operation The operation name to validate
     * @throws IllegalArgumentException if the operation is invalid
     */
    private static void validateOperation(String operation) {
        if (operation == null || operation.trim().isEmpty()) {
            throw new IllegalArgumentException("Operation cannot be null or empty");
        }
    }
}
