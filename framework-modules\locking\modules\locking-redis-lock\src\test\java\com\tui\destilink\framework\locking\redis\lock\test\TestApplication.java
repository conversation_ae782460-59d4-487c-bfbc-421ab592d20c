package com.tui.destilink.framework.locking.redis.lock.test;

import com.tui.destilink.framework.locking.redis.lock.config.RedisLockAutoConfiguration;
import com.tui.destilink.framework.redis.core.cluster.ClusterCommandExecutor;
import com.tui.destilink.framework.redis.core.config.RedisCoreProperties;
import io.lettuce.core.ClientOptions;
import io.lettuce.core.RedisURI;
import io.lettuce.core.SocketOptions;
import io.lettuce.core.TimeoutOptions;
import io.lettuce.core.cluster.ClusterClientOptions;
import io.lettuce.core.cluster.ClusterTopologyRefreshOptions;
import io.lettuce.core.cluster.RedisClusterClient;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.connection.RedisClusterConfiguration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettuceClientConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;

import java.time.Duration;
import java.util.List;

/**
 * Test application for the locking-redis-lock module.
 * It is used by all Spring Boot tests in this module.
 * It enables auto-configuration and imports the test-specific Redis lock configuration.
 * <p>
 * This configuration now uses the real ClusterCommandExecutor with Redis cluster setup
 * provided by the redis-test-support module.
 */
@Configuration(proxyBeanMethods = false)
@EnableAutoConfiguration
@Import({TestRedisLockConfiguration.class, RedisLockAutoConfiguration.class})
public class TestApplication {

    /**
     * Creates a custom RedisConnectionFactory for testing
     * with explicit configuration for Redis ACL authentication.
     */
    @Bean
    @Primary
    public RedisConnectionFactory redisConnectionFactory() {
        // Create a cluster configuration
        RedisClusterConfiguration clusterConfiguration = new RedisClusterConfiguration(
                List.of("localhost:6378"));
        clusterConfiguration.setPassword("redispassword");
        clusterConfiguration.setUsername("default");
        clusterConfiguration.setMaxRedirects(3);

        // Create Lettuce client configuration with advanced options
        LettuceClientConfiguration clientConfig = LettuceClientConfiguration.builder()
                .clientOptions(ClientOptions.builder()
                        .autoReconnect(true)
                        .socketOptions(SocketOptions.builder()
                                .connectTimeout(Duration.ofSeconds(10))
                                .keepAlive(true)
                                .build())
                        .timeoutOptions(TimeoutOptions.builder()
                                .fixedTimeout(Duration.ofSeconds(10))
                                .build())
                        .build())
                .build();

        // Create the connection factory
        return new LettuceConnectionFactory(clusterConfiguration, clientConfig);
    }

    /**
     * Creates a RedisClusterClient for test environment.
     * This is necessary because LettuceConnectionFactory creates a RedisClient 
     * instead of RedisClusterClient for single-node clusters.
     * Uses explicit RedisURI.Builder for proper authentication setup.
     *
     * This bean is needed because RedisCoreAutoConfiguration depends on RedisConnectionFactory
     * which might not be available in the test context.
     */
    @Bean
    public RedisClusterClient redisClusterClient() {
        // Create Redis cluster configuration with explicit authentication
        RedisClusterConfiguration clusterConfig = new RedisClusterConfiguration(
                List.of("localhost:6378"));
        clusterConfig.setPassword("redispassword");
        clusterConfig.setUsername("default");
        clusterConfig.setMaxRedirects(3);
        
        // Create URIs from the cluster configuration
        RedisURI redisURI = RedisURI.builder()
                .withHost("localhost")
                .withPort(6378)
                .withAuthentication("default", "redispassword")
                .build();
        
        // Create the Redis Cluster client
        RedisClusterClient client = RedisClusterClient.create(redisURI);
        
        // Configure advanced client options
        client.setOptions(ClusterClientOptions.builder()
                .autoReconnect(true)
                .validateClusterNodeMembership(false)
                .topologyRefreshOptions(ClusterTopologyRefreshOptions.builder()
                        .enableAllAdaptiveRefreshTriggers()
                        .enablePeriodicRefresh(Duration.ofSeconds(30))
                        .build())
                .socketOptions(SocketOptions.builder()
                        .connectTimeout(Duration.ofSeconds(10))
                        .keepAlive(true)
                        .build())
                .timeoutOptions(TimeoutOptions.builder()
                        .fixedTimeout(Duration.ofSeconds(10))
                        .build())
                .build());
                
        return client;
    }

    /**
     * Creates the ClusterCommandExecutor for tests.
     * This is the real implementation that was previously mocked.
     * Uses the constructor that creates its own connection pools.
     *
     * Enabling this to ensure proper keyspace prefix support with our custom Redis authentication.
     */
    @Bean
    @Primary
    public ClusterCommandExecutor clusterCommandExecutor(RedisClusterClient redisClusterClient,
                                                         RedisProperties redisProperties,
                                                         RedisCoreProperties redisCoreProperties) {
        // Creating a custom ClusterCommandExecutor ensures our Redis key prefixes are correctly applied
        // This is important for proper namespace isolation in the Redis cluster
        return new ClusterCommandExecutor(redisClusterClient, redisProperties, redisCoreProperties);
    }

    /**
     * Creates the TestLockKeyUtils bean for proper lock key generation in tests.
     */
    @Bean
    public TestLockKeyUtils testLockKeyUtils(RedisCoreProperties redisCoreProperties) {
        TestLockKeyUtils utils = new TestLockKeyUtils();
        // Manually inject the RedisCoreProperties since @Autowired doesn't work in this context
        try {
            java.lang.reflect.Field field = TestLockKeyUtils.class.getDeclaredField("redisCoreProperties");
            field.setAccessible(true);
            field.set(utils, redisCoreProperties);
        } catch (Exception e) {
            throw new RuntimeException("Failed to inject RedisCoreProperties into TestLockKeyUtils", e);
        }
        return utils;
    }
}