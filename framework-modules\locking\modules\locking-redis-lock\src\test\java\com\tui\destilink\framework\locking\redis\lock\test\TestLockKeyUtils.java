package com.tui.destilink.framework.locking.redis.lock.test;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.tui.destilink.framework.locking.redis.lock.util.LockKeyBuilder;
import com.tui.destilink.framework.redis.core.config.RedisCoreProperties;

/**
 * Test utility for creating properly formatted Redis lock keys.
 * <p>
 * This utility ensures that all test lock keys follow the documented key schema:
 * {@code <prefix>:<bucketName>:__locks__:<lockType>:{<lockName>}}
 * and use the correct application prefix configured for the test environment.
 * </p>
 * <p>
 * The utility automatically handles:
 * - Proper application prefix injection from RedisCoreProperties
 * - Lock type segment inclusion for semantic isolation
 * - Hash tag formatting for Redis Cluster compatibility
 * - Test isolation through unique IDs
 * </p>
 */
@Component
public class TestLockKeyUtils {

    @Autowired
    private RedisCoreProperties redisCoreProperties;

    /**
     * Creates a properly formatted lock key for testing.
     * This method uses the correct application prefix and follows the documented key schema:
     * {@code <prefix>:<bucketName>:__locks__:<lockType>:{<lockName>}}
     *
     * @param lockType The lock type (e.g., "reentrant", "stamped", "state", "readwrite")
     * @param bucketName The bucket name for grouping locks
     * @param lockName The name of the lock
     * @return A properly formatted lock key
     */
    public String createLockKey(String lockType, String bucketName, String lockName) {
        return LockKeyBuilder
                .forBucket(redisCoreProperties.getKeyspacePrefixes().getApplicationPrefix(), bucketName)
                .buildLockKey(lockType, lockName);
    }

    /**
     * Creates a properly formatted lock key for testing with a simple lock name.
     * Uses the test class unique ID as part of the lock name for isolation.
     *
     * @param lockType The lock type (e.g., "reentrant", "stamped", "state", "readwrite")
     * @param bucketName The bucket name for grouping locks
     * @param uniqueId The unique test ID for isolation
     * @param simpleName A simple name for the lock
     * @return A properly formatted lock key
     */
    public String createLockKey(String lockType, String bucketName, String uniqueId, String simpleName) {
        String lockName = uniqueId + ":" + simpleName;
        return createLockKey(lockType, bucketName, lockName);
    }

    /**
     * Creates a reentrant lock key for testing.
     *
     * @param bucketName The bucket name for grouping locks
     * @param uniqueId The unique test ID for isolation
     * @param simpleName A simple name for the lock
     * @return A properly formatted reentrant lock key
     */
    public String createReentrantLockKey(String bucketName, String uniqueId, String simpleName) {
        return createLockKey("reentrant", bucketName, uniqueId, simpleName);
    }

    /**
     * Creates a state lock key for testing.
     *
     * @param bucketName The bucket name for grouping locks
     * @param uniqueId The unique test ID for isolation
     * @param simpleName A simple name for the lock
     * @return A properly formatted state lock key
     */
    public String createStateLockKey(String bucketName, String uniqueId, String simpleName) {
        return createLockKey("state", bucketName, uniqueId, simpleName);
    }

    /**
     * Creates a stamped lock key for testing.
     *
     * @param bucketName The bucket name for grouping locks
     * @param uniqueId The unique test ID for isolation
     * @param simpleName A simple name for the lock
     * @return A properly formatted stamped lock key
     */
    public String createStampedLockKey(String bucketName, String uniqueId, String simpleName) {
        return createLockKey("stamped", bucketName, uniqueId, simpleName);
    }

    /**
     * Creates a read-write lock key for testing.
     *
     * @param bucketName The bucket name for grouping locks
     * @param uniqueId The unique test ID for isolation
     * @param simpleName A simple name for the lock
     * @return A properly formatted read-write lock key
     */
    public String createReadWriteLockKey(String bucketName, String uniqueId, String simpleName) {
        return createLockKey("readwrite", bucketName, uniqueId, simpleName);
    }

    /**
     * Creates a lock data key for testing.
     *
     * @param lockType The lock type (e.g., "reentrant", "stamped", "state", "readwrite")
     * @param bucketName The bucket name for grouping locks
     * @param lockName The name of the lock
     * @return A properly formatted lock data key
     */
    public String createLockDataKey(String lockType, String bucketName, String lockName) {
        return LockKeyBuilder
                .forBucket(redisCoreProperties.getKeyspacePrefixes().getApplicationPrefix(), bucketName)
                .buildLockDataKey(lockType, lockName);
    }

    /**
     * Creates an unlock channel key for testing.
     *
     * @param lockType The lock type (e.g., "reentrant", "stamped", "state", "readwrite")
     * @param bucketName The bucket name for grouping locks
     * @param lockName The name of the lock
     * @return A properly formatted unlock channel key
     */
    public String createUnlockChannelKey(String lockType, String bucketName, String lockName) {
        return LockKeyBuilder
                .forBucket(redisCoreProperties.getKeyspacePrefixes().getApplicationPrefix(), bucketName)
                .buildUnlockChannelKey(lockType, lockName);
    }

    /**
     * Creates a response cache key for testing.
     *
     * @param lockType The lock type (e.g., "reentrant", "stamped", "state", "readwrite")
     * @param bucketName The bucket name for grouping locks
     * @param lockName The name of the lock
     * @param requestUuid The unique request identifier
     * @return A properly formatted response cache key
     */
    public String createResponseCacheKey(String lockType, String bucketName, String lockName, String requestUuid) {
        return LockKeyBuilder
                .forBucket(redisCoreProperties.getKeyspacePrefixes().getApplicationPrefix(), bucketName)
                .buildResponseCacheKey(lockType, lockName, requestUuid);
    }
}
