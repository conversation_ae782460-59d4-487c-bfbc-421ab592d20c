package com.tui.destilink.framework.locking.redis.lock.impl;

import com.tui.destilink.framework.locking.redis.lock.config.RedisLockProperties;
import com.tui.destilink.framework.locking.redis.lock.service.LockOwnerSupplier;
import com.tui.destilink.framework.locking.redis.lock.service.LockWatchdog;
import com.tui.destilink.framework.locking.redis.lock.service.RedisLockOperations;
import com.tui.destilink.framework.locking.redis.lock.test.TestApplication;
import com.tui.destilink.framework.test.support.core.util.TestUtils;
import com.tui.destilink.framework.test.support.redis.annotation.RedisTestSupport;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.DirtiesContext;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@RedisTestSupport(keyspacePrefixes = { "test-locks:" })
@SpringBootTest(classes = TestApplication.class, webEnvironment = SpringBootTest.WebEnvironment.NONE)
@DirtiesContext(classMode = DirtiesContext.ClassMode.BEFORE_EACH_TEST_METHOD)
public class RedisStampedLockTest {

    private static final String UNIQUE_ID = TestUtils.generateTestClassId(RedisStampedLockTest.class);

    @Autowired
    private RedisLockOperations redisLockOperations;

    @Autowired
    private LockOwnerSupplier lockOwnerSupplier;

    @Autowired
    private RedisLockProperties redisLockProperties;

    @Autowired
    private ExecutorService virtualThreadExecutor;

    @Autowired
    private LockWatchdog lockWatchdog;

    @Autowired
    private com.tui.destilink.framework.locking.redis.lock.test.TestLockKeyUtils testLockKeyUtils;

    private RedisStampedLock lock;
    private String lockKey;

    @BeforeEach
    void setUp() {
        // Use proper lock key generation with correct schema and application prefix
        lockKey = testLockKeyUtils.createStampedLockKey("test-locks", UNIQUE_ID, "stamped-test");
        lock = new RedisStampedLock(
            redisLockOperations,
            lockOwnerSupplier,
            redisLockProperties,
            lockKey,
            virtualThreadExecutor,
            lockWatchdog);
    }

    @Test
    @DisplayName("Should acquire and release write lock")
    void shouldAcquireAndReleaseWriteLock() {
        CompletableFuture<String> writeLockFuture = lock.tryWriteLockAsync();
        String stamp = writeLockFuture.join();
        assertThat(stamp).isNotNull().startsWith("W:");

        // Unlock
        CompletableFuture<Void> unlockFuture = lock.unlock(stamp);
        unlockFuture.join();
    }

    @Test
    @DisplayName("Should acquire and release read lock")
    void shouldAcquireAndReleaseReadLock() {
        CompletableFuture<String> readLockFuture = lock.tryReadLockAsync();
        String stamp = readLockFuture.join();
        assertThat(stamp).isNotNull().startsWith("R:");

        // Unlock
        CompletableFuture<Void> unlockFuture = lock.unlock(stamp);
        unlockFuture.join();
    }

    @Test
    @DisplayName("Should perform optimistic read and validate")
    void shouldPerformOptimisticReadAndValidate() {
        // Get an optimistic read stamp
        CompletableFuture<String> optimisticReadFuture = lock.tryOptimisticReadAsync();
        String stamp = optimisticReadFuture.join();
        assertThat(stamp).isNotNull().startsWith("O:");

        // Validate the stamp
        CompletableFuture<Boolean> validationFuture = lock.validateAsync(stamp);
        assertThat(validationFuture.join()).isTrue();

        // Acquire a write lock, which should invalidate the optimistic stamp
        String writeStamp = lock.tryWriteLockAsync().join();
        assertThat(writeStamp).isNotNull();

        // The original optimistic stamp should now be invalid
        CompletableFuture<Boolean> validationAfterWriteFuture = lock.validateAsync(stamp);
        assertThat(validationAfterWriteFuture.join()).isFalse();

        // Clean up
        lock.unlock(writeStamp).join();
    }

    @Test
    @DisplayName("Should support reentrant write lock")
    void shouldSupportReentrantWriteLock() {
        String stamp1 = lock.tryWriteLockAsync().join();
        assertThat(stamp1).isNotNull().startsWith("W:");

        String stamp2 = lock.tryWriteLockAsync().join();
        assertThat(stamp2).isNotNull().startsWith("W:");
        assertThat(stamp1).isNotEqualTo(stamp2);

        // Unlock first acquisition
        lock.unlock(stamp2).join();

        // Lock should still be held, but we can't easily verify without a stamp.
        // We'll try to acquire a read lock, which should fail.
        String readStamp = lock.tryReadLockAsync().join();
        assertNotNull(readStamp, "Read lock should be acquired after one write unlock");


        // Unlock second acquisition
        lock.unlock(stamp1).join();

        // Lock should be released
        String finalReadStamp = lock.tryReadLockAsync().join();
        assertNotNull(finalReadStamp, "Read lock should be acquired after all write locks are released");
        lock.unlock(finalReadStamp).join();
    }

    @Test
    @DisplayName("Should return false for isReadLock and isWriteLock")
    void shouldReturnFalseForIsLockMethods() {
        assertFalse(lock.isReadLock(), "isReadLock should always return false for RedisStampedLock");
        assertFalse(lock.isWriteLock(), "isWriteLock should always return false for RedisStampedLock");
    }

    @Test
    @DisplayName("Should create RedisStampedLockException correctly")
    void shouldCreateExceptionCorrectly() {
        String lockName = "testLock";
        String lockType = "testType";
        String lockOwnerId = "testOwner";
        String message = "Test exception message";
        Throwable cause = new RuntimeException("Test cause");

        RedisStampedLock.RedisStampedLockException exception = new RedisStampedLock.RedisStampedLockException(
            lockName, lockType, lockOwnerId, message, cause);

        assertEquals(lockName, exception.getLockName());
        assertEquals(lockType, exception.getLockType());
        assertEquals(lockOwnerId, exception.getLockOwnerId());
        assertEquals(message, exception.getMessage());
        assertEquals(cause, exception.getCause());
    }
}
