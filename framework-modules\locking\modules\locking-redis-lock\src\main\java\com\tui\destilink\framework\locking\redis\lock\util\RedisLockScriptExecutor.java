package com.tui.destilink.framework.locking.redis.lock.util;

import com.tui.destilink.framework.locking.redis.lock.config.RedisLockProperties;
import com.tui.destilink.framework.locking.redis.lock.service.RedisLockErrorHandler;
import com.tui.destilink.framework.redis.core.cluster.ClusterCommandExecutor;
import com.tui.destilink.framework.redis.core.script.ImmutableLettuceScript;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.function.Supplier;

/**
 * Utility class for executing Redis lock scripts with retry logic and error handling.
 * <p>
 * This class extracts the script execution logic from RedisLockOperationsImpl
 * to reduce file size and improve maintainability. It provides:
 * - Retry logic with exponential backoff
 * - Error classification and handling
 * - Virtual thread integration
 * - Centralized script execution
 * </p>
 */
@Slf4j
@RequiredArgsConstructor
public class RedisLockScriptExecutor {

    private final ClusterCommandExecutor clusterCommandExecutor;
    private final RedisLockProperties properties;
    private final ExecutorService virtualThreadExecutor;
    private final RedisLockErrorHandler errorHandler;
    private final RedisLockOperationsHelper helper;

    /**
     * Executes a Redis operation with retry logic and error handling.
     *
     * @param operationName The name of the operation for logging
     * @param operation The operation to execute
     * @param <T> The return type
     * @return CompletableFuture with the operation result
     */
    public <T> CompletableFuture<T> executeWithRetry(String operationName, Supplier<CompletableFuture<T>> operation) {
        return CompletableFuture.supplyAsync(() -> {
            int maxRetries = 3; // Could be configurable
            Exception lastException = null;

            for (int attempt = 0; attempt < maxRetries; attempt++) {
                try {
                    return operation.get().join();
                } catch (Exception e) {
                    lastException = e;

                    // Use error handler to classify exception as retryable or not
                    boolean isRetryable = errorHandler.isRetryableException(e);

                    if (!isRetryable || attempt >= maxRetries - 1) {
                        log.error("Operation {} failed after {} attempts (non-retryable or max retries reached)",
                                operationName, attempt + 1, e);
                        throw e;
                    }

                    // Log retry attempt
                    log.warn("Operation {} failed on attempt {}, retrying... Error: {}",
                            operationName, attempt + 1, e.getMessage());

                    // Wait before retry with exponential backoff
                    try {
                        Thread.sleep((long) Math.pow(2, attempt) * 100); // 100ms, 200ms, 400ms, etc.
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("Interrupted during retry wait", ie);
                    }
                }
            }

            // This should never be reached, but just in case
            throw new RuntimeException("Operation " + operationName + " failed after all retries", lastException);
        }, virtualThreadExecutor);
    }

    /**
     * Executes a basic lock script (tryLock, acquireLock).
     *
     * @param lockKey The lock key
     * @param ownerId The owner ID
     * @param ttl The TTL duration
     * @param script The script to execute
     * @return CompletableFuture with boolean result
     */
    public CompletableFuture<Boolean> executeBasicLockScript(String lockKey, String ownerId, Duration ttl, 
                                                           ImmutableLettuceScript<Long> script) {
        return executeWithRetry("basicLock", () -> {
            String requestUuid = helper.generateRequestUuid();
            String[] keys = helper.buildBasicLockKeys(lockKey, requestUuid);
            String[] args = helper.buildBasicLockArgs(requestUuid, ownerId, ttl.toMillis(), 
                    RedisKeyComposer.extractLockType(lockKey));

            return helper.executeScript(lockKey, script, keys, args).thenApply(result -> {
                if (result instanceof Long) {
                    Long statusCode = (Long) result;
                    return statusCode > 0; // Positive indicates success
                }
                return false;
            });
        });
    }

    /**
     * Executes an unlock script.
     *
     * @param lockKey The lock key
     * @param ownerId The owner ID
     * @param script The unlock script
     * @return CompletableFuture that completes when unlock is done
     */
    public CompletableFuture<Void> executeUnlockScript(String lockKey, String ownerId, 
                                                      ImmutableLettuceScript<Long> script) {
        return executeWithRetry("unlock", () -> {
            String requestUuid = helper.generateRequestUuid();
            String[] keys = helper.buildUnlockKeys(lockKey, requestUuid);
            String[] args = helper.buildBasicLockArgs(requestUuid, ownerId, 0L, 
                    RedisKeyComposer.extractLockType(lockKey));

            return helper.executeScript(lockKey, script, keys, args).thenAccept(result -> {
                // Parse status code
                if (result instanceof Long) {
                    Long statusCode = (Long) result;
                    if (statusCode == 0) {
                        log.warn("Lock not held by current owner: {}, {}", lockKey, ownerId);
                    } else if (statusCode == -1) {
                        log.warn("Lock held by different owner: {}, {}", lockKey, ownerId);
                    }
                }
            });
        });
    }

    /**
     * Executes a state lock script.
     *
     * @param lockKey The lock key
     * @param ownerId The owner ID
     * @param state The state value
     * @param ttl The TTL duration
     * @param script The state lock script
     * @return CompletableFuture with boolean result
     */
    public CompletableFuture<Boolean> executeStateLockScript(String lockKey, String ownerId, String state, 
                                                           Duration ttl, ImmutableLettuceScript<Long> script) {
        return executeWithRetry("stateLock", () -> {
            String requestUuid = helper.generateRequestUuid();
            String[] keys = helper.buildStateLockKeys(lockKey, requestUuid);
            String[] args = new String[] {
                requestUuid,
                helper.getResponseCacheTtlSeconds(),
                ownerId,
                String.valueOf(ttl.toMillis()),
                RedisKeyComposer.extractLockType(lockKey),
                state
            };

            return helper.executeScript(lockKey, script, keys, args).thenApply(result -> {
                if (result instanceof Long) {
                    Long statusCode = (Long) result;
                    return statusCode > 0;
                }
                return false;
            });
        });
    }

    /**
     * Executes a read-write lock script.
     *
     * @param lockKey The lock key
     * @param ownerId The owner ID
     * @param ttl The TTL duration
     * @param readerId The reader ID (for read locks)
     * @param script The read-write lock script
     * @return CompletableFuture with Long result (stamp or status)
     */
    public CompletableFuture<Long> executeReadWriteLockScript(String lockKey, String ownerId, Duration ttl, 
                                                            String readerId, ImmutableLettuceScript<Long> script) {
        return executeWithRetry("readWriteLock", () -> {
            String requestUuid = helper.generateRequestUuid();
            String[] keys = helper.buildReadWriteLockKeys(lockKey, requestUuid, readerId);
            String[] args = new String[] {
                requestUuid,
                helper.getResponseCacheTtlSeconds(),
                String.valueOf(ttl.toMillis()),
                ownerId,
                "readwrite" // Lock mode
            };

            return helper.executeScript(lockKey, script, keys, args);
        });
    }

    /**
     * Executes a simple operation script (TTL check, get value).
     *
     * @param targetKey The target key
     * @param operation The operation name
     * @param script The script to execute
     * @param <T> The return type
     * @return CompletableFuture with the result
     */
    public <T> CompletableFuture<T> executeSimpleOperationScript(String targetKey, String operation, 
                                                               ImmutableLettuceScript<T> script) {
        return executeWithRetry(operation, () -> {
            String requestUuid = helper.generateRequestUuid();
            String[] keys = helper.buildOperationCacheKeys(targetKey, operation, requestUuid);
            String[] args = helper.buildSimpleOperationArgs(requestUuid);

            return helper.executeScript(targetKey, script, keys, args);
        });
    }

    /**
     * Executes a hash field operation script.
     *
     * @param key The hash key
     * @param field The field name
     * @param operation The operation name
     * @param script The script to execute
     * @param <T> The return type
     * @return CompletableFuture with the result
     */
    public <T> CompletableFuture<T> executeHashFieldOperationScript(String key, String field, String operation, 
                                                                  ImmutableLettuceScript<T> script) {
        return executeWithRetry(operation, () -> {
            String requestUuid = helper.generateRequestUuid();
            String responseCacheKey = RedisKeyComposer.composeOperationCacheKey(key, operation + ":" + field, requestUuid);
            
            String[] keys = new String[] { key, responseCacheKey };
            String[] args = new String[] { 
                requestUuid,
                helper.getResponseCacheTtlSeconds(),
                field
            };

            return helper.executeScript(key, script, keys, args);
        });
    }
}
